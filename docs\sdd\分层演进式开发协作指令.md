# 分层演进式开发协作指令

SCAPE项目是一个典型的“分层演进式开发”项目，其特点是基础库（`logwp`）与应用层（`scape/core`）相互依赖、共同演进。在这种开发模式下，基础库的变更会引发应用层的连锁重构，而应用层的需求又会反向驱动基础库的API扩展。

这种复杂的双向互动对开发流程提出了更高的要求。本文件系统地总结了一系列专为此类项目设计的AI协作指令，旨在高效地管理跨层依赖、架构演进和知识传递。

---

### 1. 架构与设计 (Architecture & Design)

*这些指令用于在高层次上进行思考、决策和理解项目的设计哲学。*

*   **指令**: "请向我解释 `[模块/组件名]` 的架构设计思想。这个设计解决了什么具体问题？它遵循了哪些设计原则？"
    *   **意图**: 深入理解某个核心组件（如`RunContext`、`PlotProfileRegistry`）背后的设计权衡和初衷，而不仅仅是它的用法。

*   **指令**: "我有一个新功能 `[功能描述]`。请分析将它放在基础库 `logwp` 还是应用层 `scape/core` 的利弊，并给出你的建议。"
    *   **意图**: 在添加新功能时，利用AI进行架构层面的决策分析，确保新代码被放置在最合适的位置，维持分层架构的清晰性。

---

### 2. 跨层重构与演进 (Cross-Layer Refactoring & Evolution)

*这些指令用于处理因一个层级的变更而引发的、跨越多个层级的代码修改。*

*   **指令**: "当我开发应用层时，如果你发现基础API库缺少某个通用功能，请不要在应用层重复实现。你应该向我提议：‘这个功能似乎更通用，我建议将其添加到基础库 `[文件名]` 中，可以吗？’ 经我同意后，再执行这个API增强任务。"
    *   **意图**: 启动“API增强工作流”，授权AI在遵循DRY原则的前提下，主动识别并提议对基础库的改进，但在获得批准后才能执行。

*   **指令**: "我准备修改基础库中的 `[函数/类名]`。请分析这个变更的潜在影响：应用层中有哪些模块直接或间接地依赖它？"
    *   **意图**: 在修改核心API之前，进行“变更影响分析”，让AI自动追溯依赖关系，帮助评估重构的范围和风险。

*   **指令**: "基础库的API `old_function(a, b)` 已重构为 `new_function(b, a, c=None)`。请扫描 `scape/core` 包，找出所有对 `old_function` 的调用，并将其安全地重构为新的调用方式。"
    *   **意图**: 执行“API消费者批量重构”，将繁琐且易错的API迁移工作自动化，确保应用层代码与基础库的最新API保持同步。

---

### 3. 质量与生产化 (Quality & Productionization)

*这些指令用于将应用层的原型代码，提升为符合基础库质量标准的生产级代码。*

*   **指令**: "我写了一个应用层的原型函数 `[函数名]`。请为它生成一份‘生产化清单’，指导我如何将它重构并整合进基础库 `[目标模块]`，使其符合我们项目的质量标准（包括测试、文档、错误处理等）。"
    *   **意图**: 利用AI的工程经验，将一个快速实现的原型，系统化地提升为包含完整测试、文档和健壮性设计的生产级代码。

*   **指令**: "请为基础库中新增的API `[函数/类名]` 生成一个完整的测试套件，至少包含单元测试、边界情况测试和集成测试的骨架。"
    *   **意图**: 快速为新的基础库API生成高质量的测试代码框架，确保核心库的稳定性和可靠性。

---

### 4. 知识传递与维护 (Knowledge Transfer & Maintenance)

*这些指令用于记录和传递项目演进过程中的关键决策和知识，防止项目因人员变动或时间流逝而变得难以理解。*

*   **指令**: "我们决定将 `[某功能]` 从应用层下沉到基础库。请为这个决策生成一份Markdown格式的架构决策记录（ADR），说明背景、决策和后果。"
    *   **意图**: 将重要的架构决策文档化，为项目的演进留下清晰的“脚印”，便于未来追溯和理解。

*   **指令**: "基础库的 `[旧API]` 引入了不兼容的变更，将被 `[新API]` 替代。请生成一份简明的API迁移指南，帮助应用层开发者升级他们的代码。"
    *   **意图**: 当基础库发生破坏性更新时，自动生成面向开发者的迁移文档，降低应用层的升级成本。

*   **指令**: "请分析并解释 `[包名，如logwp]` 的整体目录结构和设计哲学，帮助新成员快速上手。"
    *   **意图**: 为新加入项目的成员生成一份高层次的导览文档，帮助他们快速建立对项目架构的宏观理解。

---

### 5. 测试与验证 (Testing & Validation)

*这些指令用于确保在一个层级进行变更时，不会意外破坏其他层级的功能。*

*   **指令**: "基础库的 `[函数/类名]` 已变更。请为我生成一份针对应用层的**回归测试计划**，找出 `scape/core` 中最可能受影响的模块，并为它们设计关键的集成测试用例。"
    *   **意图**: 在基础库变更后，利用AI进行风险评估，自动生成针对应用层的、高价值的回归测试用例，确保核心功能未被破坏。

*   **指令**: "请为 `[组件名]` 的产物 `[产物名]` 创建一个**数据契约测试**。首先定义其数据结构Schema，然后编写一个消费者测试，验证它能正确处理符合该Schema的数据。"
    *   **意图**: 形式化地测试步骤之间的产物接口。确保即使生产者的实现改变，只要其输出数据的“契约”（Schema）不变，消费者就不会被破坏。

---

### 6. API与配置生命周期管理 (API & Configuration Lifecycle Management)

*这些指令用于管理API和配置的平滑演进、废弃和迁移。*

*   **指令**: "我计划废弃基础库中的 `old_function()`。请帮我执行**API废弃流程**：在函数上添加废弃警告（如`@deprecated`装饰器），更新其文档说明迁移路径，并找出所有调用点以便后续重构。"
    *   **意图**: 自动化处理API的平滑过渡。当一个API需要被替代而非立即移除时，此指令能自动完成标记废弃、通知用户和定位待办重构点的全套流程。

*   **指令**: "我们为 `[Config类名]` 新增了参数 `[参数名]` 并移除了 `[旧参数名]`。请生成一个配置迁移脚本，它可以读取旧格式的JSON/YAML配置文件，并将其安全地转换为新格式。"
    *   **意图**: 当代码重构导致配置文件结构变化时，自动生成一个一次性的迁移工具，帮助用户或系统平滑升级旧的配置文件，避免手动修改的繁琐和错误。
