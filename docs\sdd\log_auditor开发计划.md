# LogAuditor 组件开发计划与设计说明书

**版本: 1.0**

## 1. 目标与定位

### 1.1. 项目目标

开发一个名为 `LogAuditor` 的新组件，位于 `logwp.extras.ml.log_auditor`。该组件旨在提供一个标准化的、可重用的**通用分析步骤 (Generic Step)**，用于对测井曲线进行系统化的异常值检测和数据质量审计。

`LogAuditor` 的核心任务是根据用户配置的一系列审计方法，自动识别并标记出单条或多条曲线组合中的异常数据点。它将生成一套完整的、可交付的**量化报告**、**可视化图表**以及**带有异常标记的数据集**，为后续的数据清洗和机器学习建模提供关键支持。

### 1.2. 架构定位

*   **框架遵从性**: `LogAuditor` 将严格遵循《可追踪机器学习组件开发框架》进行开发，确保其成为一个模块化、可追踪、可复现的`Step`。
*   **通用性**: 与 `log_scout` 类似，`LogAuditor` 是一个**通用组件**。它不依赖于任何特定的科学模型或业务逻辑，可以被集成到任何需要进行数据质量控制的`Workflow`中。
*   **高度可配置性**: 组件的核心驱动力来自于一个灵活的配置。用户可以定义一个包含多个“审计任务”的列表，每个任务可以指定不同的异常检测算法、作用于不同的曲线，并设置各自的参数。

---

## 2. 核心概念与框架遵从性

下表将《框架》中的核心概念与`LogAuditor`组件中的具体实现进行映射：

| 框架概念 | `logwp.extras.ml.log_auditor` 中的具体实现 |
| :--- | :--- |
| **单一步骤包** | `logwp.extras.ml.log_auditor` 整个包 |
| **步骤 (Step)** | 测井曲线异常值审计步骤 |
| **门面 (Facade)** | `facade.py` |
| **主执行函数** | `run_log_auditor_step()` |
| **配置 (Config)** | `config.LogAuditorConfig` (包含一个审计任务列表) |
| **内部逻辑 (Internal)** | `internal/auditors.py` (封装各类异常检测算法) <br> `internal/plotter.py` (绘图逻辑) |
| **产物常量** | `constants.LogAuditorArtifacts` (Enum) |
| **产物处理器** | `artifact_handler.LogAuditorArtifactHandler` |
| **绘图复现** | `plotting.py` (包含多个`replot_*`函数) |
| **绘图配置** | `plot_profiles.py` (注册`PlotProfile`模板) |
| **数据快照** | 所有图表在生成前，都会先保存其绘图所需的数据为`.csv`快照。 |

---

## 3. 拟定目录结构

`LogAuditor` 将遵循标准的单一步骤包结构：

```
logwp/extras/ml/log_auditor/
├── __init__.py               # 【必须】导出公共API
├── README.md                 # (待编写) 组件使用说明文档
├── facade.py                 # 【必须】定义 run_log_auditor_step() 函数
├── config.py                 # 【必须】定义 LogAuditorConfig 及所有审计任务的Pydantic模型
├── constants.py              # 【推荐】定义产物名称和绘图配置名称常量
├── artifact_handler.py       # 【推荐】定义无状态的 LogAuditorArtifactHandler
├── plotting.py               # (可选) 定义从数据快照复现图表的功能
├── plot_profiles.py          # (可选) 注册本模块专属的PlotProfile
└── internal/                 # 【必须】存放所有内部实现细节的目录
    ├── __init__.py
    ├── auditors.py           # 核心审计算法实现 (统计、滑动窗口、ML等)
    └── plotter.py            # 核心绘图逻辑
```

---

## 4. API 设计 (Facade & Configuration)

这是`LogAuditor`设计的核心，旨在实现用户所期望的灵活性。

### 4.1. 配置模型 (`config.py`)

我们将使用Pydantic的**判别联合体 (Discriminated Unions)** 来构建一个类型安全且高度灵活的配置系统。

```python
# logwp/extras/ml/log_auditor/config.py
from typing import List, Literal, Union, Optional
from pydantic import BaseModel, Field, conlist

# 1. 定义所有审计任务的基类
class BaseAuditTask(BaseModel):
    """所有审计任务的基类配置。"""
    task_name: str = Field(..., description="审计任务的唯一名称，用于命名产物。")
    target_curves: conlist(str, min_length=1) = Field(..., description="此任务要审计的一条或多条曲线。")

# 2. 为每种算法定义具体的配置模型
class IQRAuditConfig(BaseAuditTask):
    method: Literal["iqr"] = "iqr"
    multiplier: float = Field(1.5, description="IQR乘数，用于定义异常边界。")

class ZScoreAuditConfig(BaseAuditTask):
    method: Literal["z_score"] = "z_score"
    threshold: float = Field(3.0, description="Z-Score的阈值。")

class MovingWindowAuditConfig(BaseAuditTask):
    method: Literal["moving_window"] = "moving_window"
    window_size: int = Field(21, description="滑动窗口的大小，必须为奇数。", gt=1)
    inner_method: Literal["iqr", "z_score"] = Field("iqr", description="在窗口内使用的统计方法。")
    center: bool = Field(True, description="窗口是否以当前点为中心。")

class IsolationForestAuditConfig(BaseAuditTask):
    method: Literal["isolation_forest"] = "isolation_forest"
    contamination: float = Field(0.01, description="数据集中异常点的比例估计。", gt=0, lt=0.5)

class RegressionResidualAuditConfig(BaseAuditTask):
    method: Literal["regression_residual"] = "regression_residual"
    # 对于回归法，target_curves是待预测的目标，通常只有一条
    feature_curves: conlist(str, min_length=1) = Field(..., description="用于构建回归模型的输入特征。")
    model_type: Literal["xgboost", "linear"] = Field("xgboost", description="使用的回归模型类型。")
    residual_threshold: float = Field(3.0, description="残差的标准差倍数阈值。")

class AutoencoderAuditConfig(BaseAuditTask):
    method: Literal["autoencoder"] = "autoencoder"
    encoding_dim: int = Field(2, description="自编码器中间压缩层的维度。", gt=0)
    epochs: int = Field(50, description="模型训练的轮次。", gt=0)
    batch_size: int = Field(32, description="训练批次大小。", gt=0)
    contamination: float = Field(0.01, description="数据集中异常点的比例估计，用于设置重构误差的阈值。", gt=0, lt=0.5)

class OneClassSVMAuditConfig(BaseAuditTask):
    method: Literal["one_class_svm"] = "one_class_svm"
    kernel: Literal["rbf", "linear", "poly", "sigmoid"] = Field("rbf", description="SVM使用的核函数。")
    nu: float = Field(0.01, description="异常点比例的上限，与contamination类似。", gt=0, lt=1)
    gamma: Union[float, Literal["scale", "auto"]] = Field("auto", description="核函数系数，仅用于'rbf', 'poly', 'sigmoid'核。")


# 3. 定义主配置模型，它包含一个任务列表
class LogAuditorConfig(BaseModel):
    """LogAuditor步骤的主配置模型。"""
    audit_tasks: List[
        Union[
            IQRAuditConfig,
            ZScoreAuditConfig,
            MovingWindowAuditConfig,
            IsolationForestAuditConfig,
            RegressionResidualAuditConfig,
            AutoencoderAuditConfig,
            OneClassSVMAuditConfig,
        ]
    ] = Field(..., discriminator="method", description="要执行的审计任务列表。")

    output_flag_suffix: str = Field("_is_outlier", description="添加到输出DataFrame中的异常标记列的后缀。")
```

### 4.2. 门面函数 (`facade.py`)

门面函数将接收这个配置对象，并循环执行所有任务。

```python
# logwp/extras/ml/log_auditor/facade.py
from typing import Dict, Optional
import pandas as pd
from .config import LogAuditorConfig
from logwp.extras.tracking import RunContext
from logwp.extras.plotting import PlotProfile

def run_log_auditor_step(
    config: LogAuditorConfig,
    ctx: RunContext,
    data: pd.DataFrame,
    *,
    prefix: str,
    plot_profiles: Optional[Dict[str, PlotProfile]] = None
) -> Dict[str, pd.DataFrame]:
    """
    执行LogAuditor异常值审计步骤。

    Args:
        config: 步骤的Pydantic配置对象，包含一个审计任务列表。
        ctx: 当前运行的上下文，用于追踪。
        data: 包含所有待审计曲线的Pandas DataFrame。
        prefix: 产物输出目录的前缀。
        plot_profiles: (可选) 一个字典，用于传入自定义的PlotProfile对象覆盖默认样式。

    Returns:
        一个字典，包含带有异常标记列的最终DataFrame。
    """
    # ... 内部实现将遍历 config.audit_tasks ...
    # ... 对每个任务，调用 internal.auditors 中的相应函数 ...
    # ... 生成报告、图表和最终的DataFrame ...
    pass
```

---

## 5. 产物设计 (Artifacts)

`LogAuditor`将生成一套结构化、可追溯的产物，保存在 `/[prefix]_log_auditor/` 目录下。

### 5.1. 核心产物

1.  **带异常标记的数据集 (DataFrame with Flags)**
    *   **逻辑名**: `log_auditor.datasets.data_with_outlier_flags`
    *   **文件名**: `data_with_outlier_flags.csv`
    *   **描述**: 这是最重要的产物。它是在原始输入`DataFrame`的基础上，为每个被审计的曲线（根据`task_name`）增加一个布尔类型的标记列（如 `GR_iqr_task_is_outlier`），指示该点是否被识别为异常。

2.  **审计总览报告 (Summary Report)**
    *   **逻辑名**: `log_auditor.reports.summary`
    *   **文件名**: `outlier_summary.csv`
    *   **描述**: 一个总览表格，每一行对应一个审计任务，记录了任务名称、使用方法、目标曲线、参数、检测出的异常点数量和比例等信息。

### 5.2. 详细产物 (按任务组织)

每个审计任务 (`task_name`) 都会在 `/[prefix]_log_auditor/` 下生成一个以其名称命名的子目录，存放其专属的详细产物。

```
/[prefix]_log_auditor/
├── [task_name_1]/
|   ├── report.csv
|   ├── outlier_plot_[curve1].png
|   └── outlier_plot_[curve2].png
├── [task_name_2]/
|   ├── report.csv
|   ├── outlier_plot_[curve3].png
|   ├── residual_crossplot.png
|   └── ...
└── ...
```

*   **详细报告 (`report.csv`)**:
    *   **逻辑名 (前缀)**: `log_auditor.reports.details` (e.g., `log_auditor.reports.details.iqr_on_gr`)
    *   **描述**: 包含每个被标记为异常的点的详细信息，如深度、原始值、以及判定其为异常的得分（如Z-Score值、残差大小、重构误差等）。

*   **可视化图表 (`.png`)**:
    *   **逻辑名 (前缀)**: `log_auditor.plots`
    *   **主要图表类型**:
        1.  **异常高亮图 (`outlier_highlight_plot`)**:
            *   **适用**: 所有方法。
            *   **内容**: 绘制单条曲线，并将检测出的异常点用不同颜色或标记突出显示。对于滑动窗口法，可以额外绘制动态的上下边界。
        2.  **残差交会图 (`residual_crossplot`)**:
            *   **适用**: `regression_residual` 方法。
            *   **内容**: 绘制模型预测值 vs 真实值的交会图，并将残差过大的异常点突出显示。
        3.  **特征交会图矩阵 (`feature_pairplot`)**:
            *   **适用**: 多维ML方法，如 `isolation_forest`。
            *   **内容**: 绘制所有输入特征的Pairplot，并将检测出的异常点（样本）用不同颜色高亮。
        4.  **重构误差图 (`reconstruction_error_plot`)**:
            *   **适用**: `autoencoder` 方法。
            *   **内容**: 绘制重构误差的直方图，并用一条竖线标出用于区分正常/异常的阈值。


### 5.3. 产物常量 (`constants.py`)

所有产物的逻辑名都将在`constants.py`中定义，特别是对于动态生成的产物，将定义其前缀。

```python
# logwp/extras/ml/log_auditor/constants.py
from enum import Enum

class LogAuditorArtifacts(str, Enum):
    """定义LogAuditor步骤的所有产物逻辑名称。"""
    # 核心产物
    DATA_WITH_FLAGS = "log_auditor.datasets.data_with_outlier_flags"
    SUMMARY_REPORT = "log_auditor.reports.summary"

    # 详细产物 (前缀模式)
    # 完整逻辑名将在运行时生成，如: log_auditor.reports.details.[task_name]
    TASK_REPORT_PREFIX = "log_auditor.reports.details"
    TASK_PLOT_PREFIX = "log_auditor.plots"
    TASK_DATA_SNAPSHOT_PREFIX = "log_auditor.data_snapshots"

class LogAuditorPlotTypes(str, Enum):
    """定义绘图类型，用于plot_profiles字典的键。"""
    OUTLIER_HIGHLIGHT = "outlier_highlight_plot"
    RESIDUAL_CROSSPLOT = "residual_crossplot"
    FEATURE_PAIRPLOT = "feature_pairplot"
    RECONSTRUCTION_ERROR_PLOT = "reconstruction_error_plot"
```

---

## 6. 绘图策略 (`plot_profiles.py`)

我们将为`LogAuditor`产出的所有图表类型定义高质量的默认`PlotProfile`模板，并注册到全局`plot_registry`中。

**拟注册的PlotProfile模板:**
*   `log_auditor.base`: 模块级基础模板。
*   `log_auditor.outlier_highlight`: 用于异常高亮图，定义正常点和异常点的样式。
*   `log_auditor.residual_crossplot`: 用于残差交会图。
*   `log_auditor.feature_pairplot`: 用于特征交会图矩阵。

---

## 7. 开发步骤与任务分解 (小步快跑)

### **协作模式说明**

本开发计划将采用人机协作的模式完成：
*   **您 (开发者)**: 负责执行环境准备、文件创建、代码应用、运行测试等操作。
*   **我 (Gemini Code Assist)**: 负责提供所有核心模块的编码实现和重构方案。

下文中的每一项任务都将明确我们各自的职责。

---

### **里程碑1: 基础架构与简单统计方法**

*   **任务1.1: 项目骨架搭建与配置API实现**
    *   **协作模式**: 您负责创建目录和空文件，为后续编码工作准备好基础环境。我将提供`config.py`的完整代码。
    *   **操作 (由您完成)**: 在 `logwp/extras/ml/` 目录下创建 `log_auditor` 目录及所有在“拟定目录结构”一节中列出的必需文件（此时可为空文件）。
    *   **操作 (由我完成)**: 提供 `config.py` 中 `LogAuditorConfig` 和所有审计任务Pydantic模型的完整实现代码。
    *   **验证**: 单元测试可以成功实例化一个包含多种任务的复杂`LogAuditorConfig`对象。
*   **任务1.2: Facade主流程与产物处理器实现**
    *   **协作模式**: 我将提供 `facade.py` 的主循环框架和 `artifact_handler.py` 的实现代码，您负责应用。
    *   **操作 (由我完成)**: 提供 `facade.py` 和 `artifact_handler.py` 的代码。
    *   **验证**: 集成测试可以传入一个空的`audit_tasks`列表并成功运行。
*   **任务1.3: 实现简单统计审计方法**
    *   **协作模式**: 我将提供 `internal/auditors.py` 中简单统计方法的实现代码，您负责应用和编写单元测试。
    *   **操作 (由我完成)**: 提供 `_audit_by_iqr`, `_audit_by_z_score` 等纯函数的代码。
    *   **验证**: 为每个审计函数编写单元测试。
*   **任务1.4: 端到端集成 (统计方法)**
    *   **协作模式**: 我将提供 `facade.py` 中集成统计方法的代码，您负责应用和编写集成测试。
    *   **操作 (由我完成)**: 提供 `facade.py` 中调用统计方法并生成产物的完整逻辑。
    *   **验证**: 编写一个完整的集成测试，验证所有`.csv`文件都已正确生成。

### **里程碑2: 滑动窗口与可视化**

*   **任务2.1: 实现滑动窗口审计方法**
    *   **操作**: 在`internal/auditors.py`中实现`_audit_by_moving_window`。
    *   **验证**: 单元测试验证其在边缘情况下的行为。
*   **任务2.2: 实现绘图逻辑与模板**
    *   **操作**: 实现`internal/plotter.py`和`plot_profiles.py`。首先实现`plot_outlier_highlight`。
    *   **验证**: 单元测试调用绘图函数能返回`Figure`对象。
*   **任务2.3: 端到端集成 (可视化)**
    *   **操作**: 在`facade`中调用绘图逻辑，遵循“数据快照”原则，生成并注册所有图表产物。
    *   **验证**: 扩充集成测试，验证所有预期的`.png`和数据快照`.csv`文件都已创建。

### **里程碑3: 机器学习方法**

*   **任务3.1: 实现Isolation Forest审计方法**
    *   **操作**: 在`internal/auditors.py`中实现`_audit_by_isolation_forest`。
    *   **验证**: 单元测试。
*   **任务3.2: 实现Regression Residual审计方法**
    *   **操作**: 在`internal/auditors.py`中实现`_audit_by_regression_residual`。
    *   **验证**: 单元测试。
*   **任务3.3: (可选) 实现更高级的ML方法**
    *   **操作**: 实现`Autoencoder`, `OneClassSVM`等。
*   **任务3.4: 最终集成与文档**
    *   **操作**: 完善所有方法的集成，编写`README.md`文档。
    *   **验证**: 最终的功能验收测试，确保所有配置的审计任务都能按预期工作。

---

## 8. 潜在风险与解决方案

1.  **性能问题**:
    *   **风险**: 机器学习方法（特别是`LOF`和`Autoencoder`）在处理长曲线或高维特征时可能非常耗时。
    *   **解决方案**:
        *   在文档中明确指出不同方法的计算复杂度。
        *   在`facade`中为每个耗时任务添加清晰的日志，告知用户当前进度。
        *   考虑为大数据集默认使用`IsolationForest`等更高效的算法。

2.  **参数敏感性**:
    *   **风险**: 许多ML算法（如`DBSCAN`, `LOF`）的参数难以设定。`contamination`参数也需要用户对数据有先验知识。
    *   **解决方案**:
        *   为所有参数提供经过验证的、合理的默认值。
        *   在`README.md`中为关键参数提供详细的设置指南和经验法则。

3.  **依赖管理**:
    *   **风险**: `LogAuditor`将引入新的核心依赖，如`scikit-learn`, `xgboost`，甚至可能是`tensorflow`或`pytorch`（如果使用Autoencoder）。
    *   **解决方案**: 必须将所有依赖及其版本范围清晰地添加到项目的`pyproject.toml`中。对于重量级依赖（如`pytorch`），可以考虑将其设为可选依赖项。

4.  **结果的合并与覆盖**:
    *   **风险**: 用户可能在多个任务中审计同一条曲线（例如，先用IQR再用滑动窗口）。如何合并这些结果？
    *   **解决方案**:
        *   **策略**: 采用“或”逻辑。只要一个点在**任何**一个任务中被标记为异常，它在最终的带标记DataFrame中就应被视为异常。
        *   **实现**: 在`facade`中维护一个最终的异常标记DataFrame。每完成一个任务，就将其结果（布尔标记）通过逻辑或操作（`|`）合并到主标记中。输出的标记列名应包含`task_name`以区分来源，例如`GR_iqr_task_is_outlier`和`GR_moving_window_task_is_outlier`。

---

## 9. 结论

本开发计划详细定义了`LogAuditor`组件的设计、API、产物和开发步骤。通过其高度灵活的配置系统和全面的算法支持，`LogAuditor`将成为数据预处理流程中一个不可或缺的、强大的数据质量保证工具。
