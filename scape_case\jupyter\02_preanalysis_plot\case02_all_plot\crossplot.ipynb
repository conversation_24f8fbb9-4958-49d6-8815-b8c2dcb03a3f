{"cells": [{"cell_type": "markdown", "id": "0556fffe", "metadata": {}, "source": ["# 各种交会图"]}, {"cell_type": "code", "execution_count": null, "id": "c88aa011", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "from logwp.extras.petroplot.common import *\n", "\n", "from logwp.extras.petroplot.nmr_ternary import create_publication_ready_perm_config as nmr_ternary_create_publication_ready_perm_config\n", "from logwp.extras.petroplot.nmr_ternary import run_nmr_ternary_plot_step, NmrTernaryDataSelectors\n", "\n", "from logwp.extras.petroplot.crossplot import create_publication_ready_perm_config as crossplot_create_publication_ready_perm_config\n", "from logwp.extras.petroplot.crossplot import run_crossplot_step, CrossPlotConfig\n", "\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"===库导入完成!\")"]}, {"cell_type": "markdown", "id": "07998a31", "metadata": {}, "source": ["## 加载数据"]}, {"cell_type": "code", "execution_count": null, "id": "e733775a", "metadata": {}, "outputs": [], "source": ["# --- 加载WL数据 ---\n", "from logwp.models.constants import WpDepthRole\n", "\n", "\n", "wl_data_file_path = \"./nmr_ternary.wp.xlsx\"\n", "reader = WpExcelReader()\n", "wl_project = reader.read(wl_data_file_path)\n", "print(f\"✅ 成功读取WL数据: {wl_data_file_path}\")\n", "\n", "wl_bundle = wl_project.get_dataset(\"nmr_ternary\").extract_curve_dataframe_bundle(\n", "    include_system_columns=True\n", ")\n", "wl_well_name = wl_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "wl_depth_name = wl_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "print (f\"===WL Bundle: {wl_bundle.data.head()}\")\n", "\n", "# --- 加载LWD数据 ---\n", "lwd_data_file_path = \"./lwd_nmr_ternary.wp.xlsx\"\n", "lwd_project = reader.read(lwd_data_file_path)\n", "print(f\"✅ 成功读取LWD数据: {lwd_data_file_path}\")\n", "\n", "lwd_bundle = lwd_project.get_dataset(\"lwd_nmr_ternary\").extract_curve_dataframe_bundle(\n", "    include_system_columns=True\n", ")\n", "lwd_well_name = lwd_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "lwd_depth_name = lwd_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "print (f\"===LWD Bundle: {lwd_bundle.data.head()}\")\n", "\n", "# --- 加载CrossPlot数据 ---\n", "crossplot_data_file_path = \"./crossplot.wp.xlsx\"\n", "crossplot_project = reader.read(crossplot_data_file_path)\n", "print(f\"✅ 成功读取crossplot数据: {crossplot_data_file_path}\")\n", "\n", "crossplot_bundle = crossplot_project.get_dataset(\"crossplot\").extract_curve_dataframe_bundle(\n", "    include_system_columns=True\n", ")\n", "crossplot_well_name = crossplot_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "crossplot_depth_name = crossplot_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "print (f\"===Crossplot Bundle: {crossplot_bundle.data.head()}\")"]}, {"cell_type": "markdown", "id": "7548d0c3", "metadata": {}, "source": ["## 初始化RunContext"]}, {"cell_type": "code", "execution_count": null, "id": "7a6c75c6", "metadata": {}, "outputs": [], "source": ["# 初始化RunContext\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"plot\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")"]}, {"cell_type": "markdown", "id": "df8f2779", "metadata": {}, "source": ["## NMR三角形图版\n", "\n", "### WL绘图"]}, {"cell_type": "code", "execution_count": null, "id": "ab9b8ec5", "metadata": {}, "outputs": [], "source": ["plot_config, plot_profile = nmr_ternary_create_publication_ready_perm_config(\n", "    enable_background=True,\n", "    colorscale='Plasma',\n", "    font_size_pt=20,\n", "    tick_font_size_pt=20,\n", "    marker_size=18,\n", "    legend_position='right')\n", "\n", "wl_selectors = NmrTernaryDataSelectors(\n", "    macro_curve=\"VMACRO\",\n", "    micro_curve=\"VMICRO\",\n", "    meso_curve=\"VMESO\",\n", "    color_curve=\"K_LABEL\",\n", "    hover_extra_curves=[wl_well_name, wl_depth_name]\n", ")\n", "\n", "wl_results = run_nmr_ternary_plot_step(\n", "    config=plot_config,\n", "    selectors=wl_selectors,\n", "    ctx=run_context,\n", "    bundle=wl_bundle,\n", "    plot_profile=plot_profile,\n", "    prefix=\"WL\"\n", ")"]}, {"cell_type": "markdown", "id": "b2dbb1ee", "metadata": {}, "source": ["### LWD绘图"]}, {"cell_type": "code", "execution_count": null, "id": "8e3d8b01", "metadata": {}, "outputs": [], "source": ["lwd_selectors = NmrTernaryDataSelectors(\n", "    macro_curve=\"VMACRO_LWD\",\n", "    micro_curve=\"VMICRO_LWD\",\n", "    meso_curve=\"VMESO_LWD\",\n", "    color_curve=\"K_LABEL\",\n", "    hover_extra_curves=[wl_well_name, wl_depth_name]\n", ")\n", "\n", "lwd_results = run_nmr_ternary_plot_step(\n", "    config=plot_config,\n", "    selectors=lwd_selectors,\n", "    ctx=run_context,\n", "    bundle=lwd_bundle,\n", "    plot_profile=plot_profile,\n", "    prefix=\"LWD\"\n", ")"]}, {"cell_type": "markdown", "id": "7044ed8a", "metadata": {}, "source": ["## 各种交会图"]}, {"cell_type": "code", "execution_count": null, "id": "370db592", "metadata": {}, "outputs": [], "source": ["plot_config, plot_profile = crossplot_create_publication_ready_perm_config(\n", "    bundle_name=\"main\",\n", "    x_curve=\"RD_LWD\",\n", "    y_curve=\"RS_LWD\",\n", "    color_curve=\"K_LABEL\",\n", "    x_title=\"RD_LWD(ohm.m)\",\n", "    y_title=\"RS_LWD(ohm.m)\",\n", "    x_range=(0.2,2000),\n", "    y_range=(0.2,2000),\n", "    x_log=True,\n", "    y_log=True,\n", "    show_marginal_x=True,\n", "    show_marginal_y=True,\n", "    show_diagonal_line=True,\n", "    title = None,\n", "    colorscale= \"Plasma\",\n", "    cmin = -2.0,\n", "    cmax = 3.0,\n", "    distinguish_null_color =True,\n", ")\n", "\n", "results = run_crossplot_step(\n", "    config=plot_config,\n", "    ctx=run_context,\n", "    prefix=\"rd_rs_lwd\",\n", "    bundles={\n", "        \"main\": crossplot_bundle\n", "    },\n", "    plot_profile=plot_profile\n", ")"]}, {"cell_type": "markdown", "id": "41bba618", "metadata": {}, "source": ["## 结束"]}, {"cell_type": "code", "execution_count": null, "id": "fd4e8cc9", "metadata": {}, "outputs": [], "source": ["run_context.finalize()"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}