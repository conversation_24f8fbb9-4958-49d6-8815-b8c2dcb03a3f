# LogScout 组件开发计划与设计说明书

**版本: 1.0**

## 1. 目标与定位

### 1.1. 项目目标

开发一个名为 `LogScout` 的新组件，位于 `logwp.extras.ml.log_scout`。该组件旨在提供一个标准化的、可重用的**通用分析步骤 (Generic Step)**，用于对测井数据集进行系统化的探索性分析。

`LogScout` 的核心任务是快速诊断**输入特征之间的关系**、**每个输入特征与目标特征的关系**，并识别潜在的多重共线性。它将生成一套完整的、可交付的**量化报告**和**可视化图表**产物，为后续的机器学习建模提供数据洞察和决策支持。

### 1.2. 架构定位

*   **框架遵从性**: `LogScout` 将严格遵循《可追踪机器学习组件开发框架》进行开发，确保其成为一个模块化、可追踪、可复现的`Step`。
*   **通用性**: 与 `scape.core` 中面向特定应用（如`swift_pso`）的组件不同，`LogScout` 是一个**通用组件**。它不依赖于任何特定的科学模型或业务逻辑，可以被集成到任何需要进行特征分析的机器学习`Workflow`中。
*   **可配置性**: 组件的行为将通过Pydantic模型进行配置，绘图样式将通过`logwp.extras.plotting`系统进行管理，确保其灵活性和易用性。

---

## 2. 核心概念与框架遵从性

下表将《框架》中的核心概念与`LogScout`组件中的具体实现进行映射：

| 框架概念 | `logwp.extras.ml.log_scout` 中的具体实现 |
| :--- | :--- |
| **单一步骤包** | `logwp.extras.ml.log_scout` 整个包 |
| **步骤 (Step)** | 特征与目标关系诊断步骤 |
| **门面 (Facade)** | `facade.py` |
| **主执行函数** | `run_log_scout_step()` |
| **配置 (Config)** | `config.LogScoutConfig` (Pydantic模型) |
| **内部逻辑 (Internal)** | `internal/computer.py` (定量计算) <br> `internal/plotter.py` (绘图逻辑) |
| **产物常量** | `constants.LogScoutArtifacts` (Enum) |
| **产物处理器** | `artifact_handler.LogScoutArtifactHandler` |
| **绘图复现** | `plotting.py` (包含多个`replot_*`函数) |
| **绘图配置** | `plot_profiles.py` (注册`PlotProfile`模板) |
| **数据快照** | 所有图表在生成前，都会先保存其绘图所需的数据为`.csv`快照。 |

---

## 3. 拟定目录结构

`LogScout` 将遵循标准的单一步骤包结构：

```
logwp/extras/ml/log_scout/
├── __init__.py               # 【必须】导出公共API
├── README.md                 # (待编写) 组件使用说明文档
├── facade.py                 # 【必须】定义 run_log_scout_step() 函数
├── config.py                 # 【必须】定义 LogScoutConfig Pydantic模型
├── constants.py              # 【推荐】定义产物名称和绘图配置名称常量
├── artifact_handler.py       # 【推荐】定义无状态的 LogScoutArtifactHandler
├── plotting.py               # (可选) 定义从数据快照复现图表的功能
├── plot_profiles.py          # (可选) 注册本模块专属的PlotProfile
└── internal/                 # 【必须】存放所有内部实现细节的目录
    ├── __init__.py
    ├── computer.py           # 核心定量计算逻辑 (相关性, VIF, 互信息)
    └── plotter.py            # 核心绘图逻辑 (调用seaborn和matplotlib)
```

---

## 4. API 设计 (Facade & Configuration)

### 4.1. 配置模型 (`config.py`)

```python
# logwp/extras/ml/log_scout/config.py
from pydantic import BaseModel, Field

class LogScoutConfig(BaseModel):
    """LogScout步骤的配置模型。"""
    pairplot_max_features: int = Field(
        10,
        description="自动绘制散点图矩阵(pairplot)的特征数量上限。超过此数量将跳过绘图以防止性能问题。",
        gt=1
    )
```

### 4.2. 门面函数 (`facade.py`)

```python
# logwp/extras/ml/log_scout/facade.py
from typing import List, Literal, Dict, Optional
import pandas as pd
from .config import LogScoutConfig
from logwp.extras.tracking import RunContext
from logwp.extras.plotting import PlotProfile

def run_log_scout_step(
    config: LogScoutConfig,
    ctx: RunContext,
    data: pd.DataFrame,
    features: List[str],
    target: str,
    *,
    task_type: Literal["regression", "classification"],
    prefix: str,
    plot_profiles: Optional[Dict[str, PlotProfile]] = None
) -> Dict[str, str]:
    """
    执行LogScout特征分析步骤。

    Args:
        config: 步骤的Pydantic配置对象。
        ctx: 当前运行的上下文，用于追踪。
        data: 包含所有特征和目标列的Pandas DataFrame。
        features: 需要分析的输入特征列名列表。
        target: 目标特征的列名。
        task_type: 任务类型，'regression' 或 'classification'。
        prefix: 产物输出目录的前缀，用于在同一Workflow中多次运行时区分产物。
        plot_profiles: (可选) 一个字典，用于传入自定义的PlotProfile对象覆盖默认样式。
                       键为绘图类型（如 'heatmap', 'regplot'），值为PlotProfile实例。

    Returns:
        一个包含分析状态的字典。
    """
    # ... 实现将在开发步骤中填充 ...
    pass
```

---

## 5. 产物设计 (Artifacts)

### 5.1. 产物常量 (`constants.py`)

我们将为所有产物定义清晰、唯一的逻辑名称。对于按特征生成的图表，将使用**前缀模式**。

```python
# logwp/extras/ml/log_scout/constants.py
from enum import Enum

class LogScoutArtifacts(str, Enum):
    """定义LogScout步骤的所有产物逻辑名称。"""
    # 定量报告
    PEARSON_CORR = "log_scout.reports.pearson_correlation"
    SPEARMAN_CORR = "log_scout.reports.spearman_correlation"
    VIF_SCORES = "log_scout.reports.vif_scores"
    MUTUAL_INFO = "log_scout.reports.mutual_information"

    # 数据快照 (用于绘图复现)
    PEARSON_CORR_DATA = "log_scout.data_snapshots.pearson_correlation"
    SPEARMAN_CORR_DATA = "log_scout.data_snapshots.spearman_correlation"
    PAIRPLOT_DATA = "log_scout.data_snapshots.pairplot_data"
    # 特征与目标关系图的数据快照 (前缀模式)
    TARGET_RELATIONSHIP_DATA_PREFIX = "log_scout.data_snapshots.target_relationship"

    # 特征间关系图
    PEARSON_HEATMAP = "log_scout.plots.pearson_heatmap"
    PEARSON_CLUSTERMAP = "log_scout.plots.pearson_clustermap"
    SPEARMAN_HEATMAP = "log_scout.plots.spearman_heatmap"
    SPEARMAN_CLUSTERMAP = "log_scout.plots.spearman_clustermap"
    PAIRPLOT = "log_scout.plots.pairplot"

    # 特征与目标关系图 (前缀模式)
    # 实际名称将在运行时动态生成，如: log_scout.plots.target_relationship.regplot_GR
    TARGET_RELATIONSHIP_PLOT_PREFIX = "log_scout.plots.target_relationship"

class LogScoutPlotTypes(str, Enum):
    """定义绘图类型，用于plot_profiles字典的键。"""
    PEARSON_HEATMAP = "pearson_heatmap"
    PEARSON_CLUSTERMAP = "pearson_clustermap"
    SPEARMAN_HEATMAP = "spearman_heatmap"
    SPEARMAN_CLUSTERMAP = "spearman_clustermap"
    PAIRPLOT = "pairplot"
    REGRESSION_PLOT = "regression_plot" # 回归任务散点图
    BOX_PLOT = "box_plot"               # 分类任务箱形图

class LogScoutPlotProfiles(str, Enum):
    """定义将在全局注册表中注册的PlotProfile模板名称。"""
    BASE = "log_scout.base"
    HEATMAP = "log_scout.heatmap"
    CLUSTERMAP = "log_scout.clustermap"
    PAIRPLOT = "log_scout.pairplot"
    REGPLOT = "log_scout.regplot"
    BOXPLOT = "log_scout.boxplot"
```

### 5.2. 产物处理器 (`artifact_handler.py`)

处理器将是无状态的，提供保存DataFrame和图表的方法。

```python
# logwp/extras/ml/log_scout/artifact_handler.py
from pathlib import Path
import pandas as pd
from matplotlib.figure import Figure

class LogScoutArtifactHandler:
    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path):
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=True) # 保留索引，因为相关性矩阵的索引是特征名

    @staticmethod
    def save_figure(fig: Figure, path: Path):
        path.parent.mkdir(parents=True, exist_ok=True)
        fig.savefig(path, dpi=300, bbox_inches='tight')
```

---

## 6. 绘图策略 (`plot_profiles.py`)

我们将为`LogScout`产出的所有图表类型定义高质量的默认`PlotProfile`模板，并注册到全局`plot_registry`中。这将确保图表风格的统一性和专业性。

**拟注册的PlotProfile模板:**
*   `log_scout.base`: 模块级基础模板，定义通用字体、尺寸等。
*   `log_scout.heatmap`: 用于常规热力图。
*   `log_scout.clustermap`: 用于聚类热力图。
*   `log_scout.pairplot`: 用于散点图矩阵。
*   `log_scout.regplot`: 用于回归任务的散点回归图。
*   `log_scout.boxplot`: 用于分类任务的箱形图。

用户可以通过`facade`函数的`plot_profiles`参数，使用“获取-修改-传入”模式进行灵活的样式定制。

---

## 7. 开发步骤与任务分解 (小步快跑)

我们将开发过程分解为以下可独立验证的微任务。

### **里程碑1: 基础架构与定量分析**

#### **任务1.1: 项目骨架搭建**
*   **操作**: 在`logwp/extras/ml/`下创建`log_scout`目录及所有必需的空文件 (`__init__.py`, `facade.py`, `config.py`, `constants.py`, `artifact_handler.py`, `internal/__init__.py`, `internal/computer.py`, `internal/plotter.py`)。
*   **验证**: 确认目录结构符合规范。

#### **任务1.2: 实现配置与常量**
*   **操作**:
    *   在`config.py`中实现`LogScoutConfig` Pydantic模型。
    *   在`constants.py`中实现`LogScoutArtifacts`和`LogScoutPlotTypes`枚举。
*   **验证**: 单元测试可以成功导入并实例化`LogScoutConfig`。

#### **任务1.3: 实现产物处理器**
*   **操作**: 在`artifact_handler.py`中实现`LogScoutArtifactHandler`的`save_dataframe`和`save_figure`静态方法。
*   **验证**: 编写单元测试，使用`pytest`的`tmp_path`来验证文件可以被正确创建和写入。

#### **任务1.4: 实现内部定量计算逻辑**
*   **操作**: 在`internal/computer.py`中实现以下纯函数：
    *   `compute_correlations(df, features)` -> `(pearson_corr, spearman_corr)`
    *   `compute_vif(df, features)` -> `vif_df`
    *   `compute_mutual_information(df, features, target, task_type)` -> `mi_df`
*   **依赖**: `pandas`, `statsmodels`, `scikit-learn`。
*   **验证**: 为每个计算函数编写单元测试，使用固定的输入`DataFrame`，断言输出结果的正确性（形状、列名、数值范围等）。

#### **任务1.5: 编排Facade (仅定量部分)**
*   **操作**:
    *   在`facade.py`的`run_log_scout_step`中，实现对`internal.computer`的调用。
    *   实现数据预处理（删除NaN）。
    *   使用`ctx.get_step_dir()`创建带前缀的输出目录。
    *   调用`ArtifactHandler`保存所有`.csv`产物。
    *   调用`ctx.register_artifact()`注册所有`.csv`产物。
*   **验证**: 编写**集成测试**，使用`MagicMock`模拟`RunContext`，调用`facade`函数，并断言`handler.save_dataframe`和`ctx.register_artifact`被以正确的参数调用。

---

### **里程碑2: 可视化与最终集成**

#### **任务2.1: 定义并注册绘图模板**
*   **操作**:
    *   创建`plot_profiles.py`文件。
    *   定义并注册所有需要的`PlotProfile`模板（`log_scout.base`, `log_scout.heatmap`, 等）。
*   **验证**: 单元测试可以从`plot_registry`中成功获取这些模板。

#### **任务2.2: 实现内部绘图逻辑**
*   **操作**: 在`internal/plotter.py`中实现以下函数，每个函数都接收数据和`PlotProfile`作为输入，并返回一个`matplotlib.figure.Figure`对象。
    *   `plot_heatmap(corr_matrix, profile)`
    *   `plot_clustermap(corr_matrix, profile)`
    *   `plot_pairplot(df, features, profile)`
    *   `plot_regression_target(df, feature, target, profile)`
    *   `plot_classification_target(df, feature, target, profile)`
*   **依赖**: `matplotlib`, `seaborn`。
*   **验证**: 编写单元测试，调用这些绘图函数。测试不检查图像的像素级内容，而是断言返回的是一个`Figure`对象，且该对象包含`Axes`。

#### **任务2.3: 编排Facade (可视化部分)**
*   **操作**:
    *   在`facade.py`中，扩充`run_log_scout_step`的逻辑。
    *   从`plot_profiles`参数或全局注册表中获取`PlotProfile`对象。
    *   调用`internal.plotter`中的函数生成图表。
    *   **遵循数据快照原则**: 在调用绘图函数前，先将绘图所需的数据（如相关性矩阵）保存为`.csv`文件，并将其作为产物注册。
    *   调用`ArtifactHandler`保存所有`.png`产物。
    *   调用`ctx.register_artifact()`注册所有`.png`产物。
*   **验证**: 扩充集成测试，验证`handler.save_figure`和`ctx.register_artifact`被正确调用。

#### **任务2.4: 端到端集成测试**
*   **操作**: 编写一个完整的集成测试，该测试：
    1.  创建一个指向临时目录的**真实`RunContext`**。
    2.  创建一个测试用的`DataFrame`。
    3.  调用`run_log_scout_step`函数。
    4.  测试结束后，断言所有预期的`.csv`和`.png`文件都已在临时目录的正确位置创建。
    5.  断言`manifest.json`文件中包含了所有已注册的产物。
*   **验证**: 这是最终的功能验收测试，确保整个`Step`按预期工作。

#### **任务2.5: 编写组件`README.md`**
*   **操作**: 参照`swift_pso`和`validation`的`README.md`，为`log_scout`组件编写一份详细的使用说明文档。
*   **验证**: 文档清晰地说明了组件的目标、API、配置、产物和使用示例。

---

## 8. 潜在风险与解决方案

1.  **性能问题**:
    *   **风险**: 当输入特征数量过多时，`pairplot`的计算和渲染会消耗大量内存和时间，可能导致程序崩溃。
    *   **解决方案**:
        *   在`LogScoutConfig`中引入`pairplot_max_features`参数，作为硬性上限。
        *   在`facade`中检查特征数量，如果超过此阈值，则记录一条`warning`日志并跳过`pairplot`的生成。

2.  **VIF计算不稳定性**:
    *   **风险**: 当特征间存在完全共线性时，VIF值为无穷大，`statsmodels`的计算函数会抛出异常或返回`inf`。
    *   **解决方案**:
        *   在`internal/computer.py`的`compute_vif`函数中，使用`try-except`块捕获潜在的数值计算异常。
        *   对于计算出`inf`的VIF值，在报告中保留为`inf`字符串，并添加备注，明确指出该特征存在完全共线性。

3.  **依赖管理**:
    *   **风险**: `LogScout`引入了新的核心依赖`statsmodels`和`seaborn`。
    *   **解决方案**: 必须将这两个库添加到项目的`pyproject.toml`或`requirements.txt`中，并明确版本范围，以确保环境的一致性。

4.  **分类任务的目标列处理**:
    *   **风险**: `mutual_info_classif`和`seaborn.boxplot`对目标列的数据类型有要求（通常是整数或字符串）。
    *   **解决方案**: 在`facade`的预处理阶段，明确检查`target`列的数据类型。如果`task_type`为`classification`，确保目标列被转换为合适的类型（如`int`或`category`），并处理可能的`NaN`值。

---

## 9. 结论

本开发计划详细定义了`LogScout`组件的设计、API、产物和开发步骤。通过遵循此计划，我们可以系统地、高质量地完成该通用分析组件的开发，确保其无缝集成到现有的机器学习组件框架中。
