# SCAPE_SAD_logwp - logwp包软件架构设计

> **版本**: 1.0
> **日期**: 2025-01-07
> **维护者**: SCAPE Core Team
> **文档类型**: 软件架构设计文档（logwp包专用）

## 1 背景与定位

### 1.1 logwp包在SCAPE项目中的定位

**SCAPE项目三层架构**：
```text
scape_case/     # 研究案例层：具体研究案例、实验复现、CLI工具、Notebook示例
scape/          # 算法层：OBMIQ、FOSTER-NMR、SWIFT-PSO等核心算法实现
logwp/          # 数据管理层：格式无关测井数据模型、文件读取、基础工具
```

**logwp包核心职责**：
- **测井数据建模**：提供格式无关的测井数据对象模型
- **文件读取处理**：支持WP、LAS等多种格式的数据读取
- **基础工具支持**：GPU计算、性能监控、日志配置等基础设施
- **功能扩展平台**：为测井通用数据处理提供扩展基础

**与其他包的关系**：
- **scape包依赖logwp**：使用logwp的数据模型进行算法计算
- **scape_case包依赖logwp+scape**：使用logwp的数据模型和scape的算法进行研究案例
- **logwp独立性**：logwp包完全独立，不依赖scape和scape_case

### 1.2 项目特点与设计原则

**SCAPE项目特点**：
- **科学研究导向**：专注算法验证、实验复现，不涉及生产部署
- **小团队快速迭代**：避免过度工程化，重点解决实际问题
- **数据敏感性**：处理石油工程敏感数据，仅限内部使用

**logwp设计原则**：
1. **格式无关优先**：数据模型独立于具体文件格式
2. **实用主义**：避免过度抽象，专注解决实际问题
3. **类型安全**：现代化类型系统，编译时错误检查
4. **性能优化**：GPU加速、异步I/O、内存优化
5. **简化架构**：避免Registry、Factory等过度工程化模式

## 2 现代化技术栈

### 2.1 核心技术选型

**Python 3.11+ 现代特性**：
- **Exception Groups (PEP 654)**：批量异常处理，提升错误诊断能力
- **性能优化**：更快的启动时间、更低的内存占用
- **类型系统增强**：更好的TypedDict支持、Self类型

**核心技术栈**：
- **ruff**：统一代码质量工具，替代black+isort+flake8+pylint组合
- **structlog**：结构化日志，JSON格式便于分析和监控
- **pydantic v2**：数据验证与序列化，性能提升5-50倍
- **pyproject.toml**：现代依赖管理，替代requirements.txt

**GPU计算集成**：
- **cupy**：GPU加速的numpy替代，无缝切换CPU/GPU计算
- **cudf**：GPU加速的pandas替代，大数据处理性能提升
- **numba**：JIT编译，支持CUDA kernel编写

### 2.2 架构现代化目标

1. **类型安全**：100%类型注解覆盖，TypedDict结构化数据
2. **性能优化**：GPU加速核心算法，异步I/O处理大文件
3. **开发效率**：ruff一键代码质量检查，现代化工具链
4. **可观测性**：结构化日志，GPU性能监控，异常追踪
5. **扩展性**：类型安全的插件系统，GPU/CPU自动切换

## 3 logwp包架构总览

### 3.1 四子包架构设计

```text
logwp/                      # 测井数据管理包
├── utils/                  # 基础工具包
│   ├── gpu/               # GPU计算工具
│   ├── logging_config.py  # 结构化日志配置
│   ├── performance.py     # 性能监控工具
│   └── case_insensitive_collections.py # CIIA集合类
├── models/                 # 格式无关的测井数据模型
│   ├── types/             # 类型定义子包
│   ├── curve/             # 曲线基本属性管理
│   ├── ext_attr/          # 扩展属性数据管理
│   ├── datasets/          # 数据集层
│   ├── internal/          # 模型层内部业务逻辑
│   ├── head.py            # WpHead 井头信息
│   ├── mapping.py         # WpWellMap 井名映射
│   └── well_project.py    # WpWellProject (组合根)
├── io/                     # 文件读取生成logwp模型
│   ├── constants.py       # I/O层格式特定常量
│   ├── exceptions.py      # I/O层异常定义
│   └── wp_excel/          # WP Excel格式支持
│       ├── wp_excel_reader.py # WP Excel主读取器
│       └── internal/      # WP Excel内部处理逻辑
│           ├── curve_parser.py     # 曲线解析器
│           ├── data_converter.py   # 数据转换器
│           ├── dataset_parser.py   # 数据集解析器
│           ├── excel_parser.py     # Excel基础解析器
│           ├── head_info_parser.py # 井头信息解析器
│           ├── validator.py        # 格式验证器
│           └── well_map_parser.py  # 井名映射解析器
└── extras/                 # logwp模型功能扩展包（目前暂时为空）
    ├── nmr/               # NMR数据处理扩展（预留）
    └── visualization/     # 可视化工具扩展（预留）
```

### 3.2 依赖关系矩阵

| 子包 | utils | models | io | extras |
|------|-------|--------|----|----|
| **utils** | — | ← | ← | ← |
| **models** |   | — | ← | ← |
| **io** |   |   | — | |
| **extras** |   |   |   | — |

**依赖约束**：
- **单向依赖**：高层只能导入低层，反向引用在CI阶段由ruff检查阻断
- **接口显式化**：跨层通信仅通过明确的API接口，避免内部实现细节泄露
- **常量异常分层**：每个子包管理自己的constants.py和exceptions.py

### 3.3 核心对象模型

**测井数据模型三层架构**：
```text
┌─────────────────────────────────────────────────────────────────┐
│                    项目管理层 (Project Layer)                    │
│  WpWellProject (组合根)                                         │
│  ├── WpHead (井头信息)                                          │
│  │   └── ExtAttributeManager (扩展属性管理)                     │
│  ├── WpWellMap (井名映射)                                       │
│  └── WpDatasets (数据集集合)                                    │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                   完整数据集层 (Full Dataset Layer)              │
│  WpDepthIndexedDatasetBase (数据集基类)                         │
│  ├── WpContinuousDataset (连续型数据集)                         │
│  ├── WpDiscreteDataset (离散型数据集)                           │
│  ├── WpIntervalDataset (区间型数据集)                           │
│  ├── CurveMetadata (曲线元数据管理)                             │
│  │   └── CurveBasicAttributes[] (曲线基本属性)                  │
│  ├── pd.DataFrame (曲线数据体)                                  │
│  └── Bundle提取方法                                             │
│      ├── extract_curve_dataframe_bundle()                      │
│      └── extract_curve_array_bundle()                          │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                 轻量级数据包层 (Bundle Layer)                    │
│  WpDatasetBundle (抽象基类)                                     │
│  ├── 基础属性                                                   │
│  │   ├── name: str (数据集名称)                                │
│  │   └── curve_metadata: CurveMetadata (曲线元数据)            │
│  ├── 便捷属性 (自动计算)                                        │
│  │   ├── well_curve_map: CaseInsensitiveDict (井名曲线映射)    │
│  │   ├── is_interval_bundle: bool (是否为区间数据集)           │
│  │   ├── depth_curve_map: Optional[Dict] (单深度映射)          │
│  │   ├── depth_top_curve_map: Optional[Dict] (顶深映射)        │
│  │   └── depth_bottom_curve_map: Optional[Dict] (底深映射)     │
│  ├── WpDataFrameBundle (DataFrame数据包)                       │
│  │   ├── data: pd.DataFrame (DataFrame数据)                    │
│  │   ├── curve_to_columns_map: Dict (曲线到列名映射)           │
│  │   └── 适用场景: 数据预处理、DataFrame操作                   │
│  └── WpArrayBundle (NumPy数组数据包)                           │
│      ├── data: CaseInsensitiveDict[str, np.ndarray] (数组数据) │
│      ├── to_sklearn_format() (ML格式转换)                      │
│      ├── get_feature_curves() (特征曲线获取)                   │
│      └── 适用场景: 算法计算、机器学习                          │
└─────────────────────────────────────────────────────────────────┘
```

**三层架构设计原则**：

**1. 项目管理层 (Project Layer)**：
- **职责**：项目级数据管理、元信息管理、数据一致性保证
- **特点**：重量级、完整功能、持久化存储
- **使用场景**：项目创建、数据导入、项目保存

**2. 完整数据集层 (Full Dataset Layer)**：
- **职责**：数据集管理、曲线元数据管理、数据验证
- **特点**：完整元数据、类型安全、格式无关
- **使用场景**：数据集操作、元数据查询、数据验证

**3. 轻量级数据包层 (Bundle Layer)**：
- **职责**：数据传输、便捷访问、算法友好
- **特点**：轻量级、高性能、DTO模式
- **使用场景**：算法计算、数据分析、机器学习

**核心组件职责**：
- **WpWellProject**：井工程项目聚合根，管理数据一致性和完整性
- **WpHead**：井头信息管理，支持扩展属性分层查找
- **WpWellMap**：井名映射管理，支持循环检测和规范化
- **WpDepthIndexedDatasetBase**：完整数据集基类，支持三种类型和Bundle提取
- **CurveMetadata**：曲线元数据管理器，提供大小写不敏感的曲线查询
- **ExtAttributeManager**：扩展属性管理器，支持6种属性类别分层查找
- **WpDatasetBundle**：轻量级数据包抽象基类，定义通用接口和便捷属性
- **WpDataFrameBundle**：DataFrame数据包，适合数据预处理场景
- **WpArrayBundle**：NumPy数组数据包，适合算法计算和机器学习场景

**数据流转模式**：
```text
┌─────────────────┐    读取/导入    ┌─────────────────┐
│   文件格式      │ ──────────────→ │  WpWellProject  │
│  (WP/LAS/JSON)  │                 │   (项目管理)    │
└─────────────────┘                 └─────────────────┘
                                            │
                                            │ 获取数据集
                                            ▼
                                    ┌─────────────────┐
                                    │ WpDatasetBase   │
                                    │  (完整数据集)   │
                                    └─────────────────┘
                                            │
                        ┌───────────────────┼───────────────────┐
                        │                   │                   │
                        ▼                   ▼                   ▼
                ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
                │ DataFrame操作   │ │   算法计算      │ │   机器学习      │
                │ 数据预处理      │ │   数值分析      │ │   特征工程      │
                └─────────────────┘ └─────────────────┘ └─────────────────┘
                        │                   │                   │
                        ▼                   ▼                   ▼
                ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
                │WpDataFrameBundle│ │  WpArrayBundle  │ │  WpArrayBundle  │
                │   (DataFrame)   │ │   (NumPy数组)   │ │ (scikit-learn)  │
                └─────────────────┘ └─────────────────┘ └─────────────────┘
```

**使用模式示例**：
```python
# 1. 项目管理层：数据导入和项目管理
project = read_wp_excel("santos_field.wp.xlsx")
dataset = project.get_dataset("OBMIQ_logs")

# 2. 完整数据集层：元数据查询和数据验证
curve_names = dataset.curve_metadata.get_analysis_suitable_curves()
validation_result = dataset.validate_numeric_data(curve_names)

# 3a. 轻量级数据包层：DataFrame预处理场景
df_bundle = dataset.extract_curve_dataframe_bundle(curve_names)
processed_df = df_bundle.data.dropna()  # pandas操作

# 3b. 轻量级数据包层：算法计算场景
array_bundle = dataset.extract_curve_array_bundle(curve_names)
feature_data = array_bundle.data["PHIT"]  # numpy操作

# 3c. 轻量级数据包层：机器学习场景
ml_bundle = dataset.extract_curve_array_bundle(curve_names + ["PERM"])
X, y = ml_bundle.to_sklearn_format(target_curve="PERM")
model.fit(X, y)  # scikit-learn操作
```

**Bundle架构设计优势**：

**1. 性能优化**：
- **避免重复创建**：Bundle避免了完整数据集对象的创建开销
- **内存友好**：只包含必要的数据和元数据，减少内存占用
- **按需提取**：只提取算法需要的曲线，避免全量数据加载

**2. 类型安全**：
- **明确的类型注解**：所有方法都有完整的类型注解，支持IDE检查
- **抽象方法约束**：确保子类正确实现必要的接口
- **编译时检查**：支持mypy等静态类型检查工具

**3. 使用便捷**：
- **自动化属性**：基于curve_metadata自动计算便捷属性
- **大小写不敏感**：所有映射都支持CIIA架构，提高易用性
- **ML友好**：专门为机器学习场景设计的辅助方法

**4. 架构清晰**：
- **DTO模式**：纯数据传输对象，职责清晰，易于理解
- **分层使用**：不同层次的数据集服务于不同的使用场景
- **格式无关**：Bundle完全独立于具体的文件格式

**5. 扩展性强**：
- **新Bundle类型**：可以轻松添加其他Bundle类型（如TensorBundle）
- **ML方法扩展**：WpArrayBundle可以继续添加更多ML辅助方法
- **元数据利用**：完整的曲线元数据为高级功能提供基础

## 4 核心设计原则与模式

### 4.1 格式无关架构设计哲学 (FAP)

**核心设计原则**：logwp是与数据格式无关的测井数据模型，不绑定任何特定的文件格式。

**格式无关原则 (Format-Agnostic Principle, FAP)**：

**FAP-1 格式无关原则**：
- `WpWellProject` ≠ WP文件的直接映射
- `WpHead` ≠ `_Head_Info`工作表的直接映射
- `WpDataset` ≠ 数据工作表的直接映射
- 支持WP、LAS、JSON、HDF5等多种格式的数据

**FAP-2 四层架构分离**：应用层→models层→I/O层→存储层，严格分层，禁止跨层依赖

**FAP-3 WFS规范定位**：设计模板而非实现约束，借鉴最佳实践，不受格式细节约束

**FAP-4 业务优先顺序**：业务需求→代码模型→格式支持，先理解业务本质，再设计模型

**FAP-5 抽象具体分离**：业务模型与格式解析器严格解耦，通过依赖注入和适配器模式实现

**FAP-11 物理约束分离**：logwp数据模型层不实现物理约束验证
- **设计原理**：数据模型保持表达的广泛性，支持各种数据状态和用途
- **职责分工**：物理约束验证属于scape算法层的业务逻辑
- **执行时机**：仅在用户执行特定算法任务时根据需要进行物理约束验证
- **架构优势**：避免数据加载时的过度约束，支持数据预处理、清洗、转换等多种用途

**架构分层设计**：
```text
scape算法层：  物理约束验证、业务规则检查、算法特定验证
              ↓
logwp模型层：  数据结构验证、格式完整性检查、基础数据类型验证
              ↓
I/O层：       wp_reader.py  las_reader.py  json_reader.py
              ↓
存储层：      WP文件       LAS文件       JSON文件
```

### 4.2 简化设计：避免过度工程化

**设计原则**：
- **直接使用具体类型**：避免Protocol接口的过度抽象
- **明确的依赖关系**：用户明确知道使用什么类型，无需注册机制
- **简化的类型系统**：使用TypedDict而非Protocol接口

**TypedDict 结构化数据**：
```python
from typing import TypedDict

class WpDataDict(TypedDict):
    name: str
    type: str
    rows: int

class WpMetaDict(TypedDict):
    curve_count: int
    well_names: list[str]
    depth_range: tuple[float, float]
```

**直接使用模式**：
```python
# 避免工厂模式，直接使用具体类型
from logwp.models.datasets import WpContinuousDataset

dataset = WpContinuousDataset(name="logs", df=data)

# 避免注册机制，直接导入需要的处理器
from logwp.models.ext_attr import T2AxisProcessor

processor = T2AxisProcessor()
result = processor.parse(t2_axis_data)
```

**直接数据集访问模式**：
```python
# 业务层直接使用数据集进行机器学习
project = read_wp_excel("data/santos_field.wp.xlsx")

# 获取数据集
logs_dataset = project.get_dataset("OBMIQ_logs")
label_dataset = project.get_dataset("K_Label")

# 直接使用DataFrame进行机器学习
X = logs_dataset.df[feature_columns]
y = label_dataset.df[target_column]

# 训练模型
model.fit(X, y)
```

### 4.3 大小写不敏感标识符架构 (CIIA)

**设计目标**：测井数据中的标识符（井名、数据集名、曲线名、属性名等）需要支持大小写不敏感比较和索引，同时保持原始显示格式。

**核心原则**：
- **格式无关性**：标识符处理完全独立于具体数据格式
- **显示与索引分离**：原始格式用于显示，规范化格式用于比较
- **国际化支持**：采用Unicode标准化和Case Folding
- **冲突检测**：同一作用域内规范化后相同的标识符必须报错

**规范化流程**：
1. **去除空格**：移除首尾空白字符
2. **Unicode归一化**：NFKC兼容性分解与组合
3. **大小写折叠**：Unicode Case Folding（优于简单大写转换）
4. **冲突检测**：检测规范化后相同但原始格式不同的标识符

**三层分离架构**：
```text
应用层：    WpWellProject.get_dataset("OBMIQ_logs")
           ↓ (大小写不敏感查找)
抽象层：    WpIdentifier(original="OBMIQ_logs", normalized="obmiq_logs")
           ↓ (规范化处理)
工具层：    WpStringNormalizer.normalize("OBMIQ_logs") → "obmiq_logs"
```

### 4.4 现代化异常处理与日志

**分层异常树 + Exception Groups (Python 3.11+)**：
```python
# 传统异常层次
WpError ─┬─ WpFileError
         ├─ WpDataError
         └─ WpValidationError

# Exception Groups 批量错误处理
def process_multiple_files(files: list[Path]) -> None:
    errors = []
    for file in files:
        try:
            process_file(file)
        except WpError as e:
            errors.append(e)
    if errors:
        raise ExceptionGroup("Multiple file processing errors", errors)
```

**结构化日志**：
- 使用 `structlog` 替代标准 logging
- JSON 格式输出，便于分析和监控
- 包含 operation、stage、dataset_name 等结构化字段

### 4.5 机器学习友好设计

**设计背景**：测井数据在机器学习应用中具有重要价值，logwp包在设计时充分考虑了机器学习的需求。

**核心设计原则**：
- **DataFrame优先原则**：以pandas DataFrame作为核心数据结构，确保与ML生态系统的无缝集成
- **列名友好原则**：提供ML友好的列名，避免括号、空格等特殊字符
- **查询灵活原则**：支持直观的数据查询语法，便于特征工程和数据筛选
- **类型一致原则**：确保数据类型的一致性，避免ML算法的类型错误

**DataFrame友好列名设计**：
```python
# 原始名称 → 友好名称（ML兼容）
"GR"             → "GR"
"T2_VALUE[1]"    → "T2_VALUE_1"
"T2_VALUE[2]"    → "T2_VALUE_2"
"GR-CORR.V2"     → "GR_CORR_V2"
"WELL NAME"      → "WELL_NAME"
```

**直接数据访问模式**：
```python
# 业务层直接使用数据集进行机器学习
project = read_wp_excel("data/santos_field.wp.xlsx")
dataset = project.get_dataset("OBMIQ_logs")

# 直接获取DataFrame用于ML
X = dataset.df[feature_columns]  # 特征数据
y = dataset.df[target_column]    # 目标变量

# 与主流ML库无缝集成
from sklearn.ensemble import RandomForestRegressor
model = RandomForestRegressor()
model.fit(X, y)

# XGBoost等库也完全兼容
import xgboost as xgb
xgb_model = xgb.XGBRegressor()
xgb_model.fit(X, y)
```

**占位符查询字符串设计**：
```python
# 用户友好的查询语法
quality_filter = "${GR} > 20 and ${GR} < 150 and ${DEN} > 1.8"
clean_query = metadata.translate_query_for_dataframe(quality_filter)
clean_data = dataset.df.query(clean_query)

# 异常检测
anomaly_filter = "${T2_VALUE[1]} > @upper_bound or ${T2_VALUE[1]} < @lower_bound"
anomaly_query = metadata.translate_query_for_dataframe(anomaly_filter)
anomalies = dataset.df.query(anomaly_query, local_dict={
    'upper_bound': np.percentile(dataset.df['T2_VALUE_1'], 95),
    'lower_bound': np.percentile(dataset.df['T2_VALUE_1'], 5)
})
```

### 4.6 内部业务逻辑层设计（Internal Logic Pattern）

**设计背景**：将复杂的内部操作提取到独立的内部逻辑层，避免核心类承担过多职责。

**核心设计原则**：
- **职责分离原则**：核心类专注数据存储和状态管理，内部逻辑层处理复杂业务逻辑
- **无状态函数原则**：内部逻辑函数设计为无状态的纯函数，只依赖传入参数
- **内部使用原则**：内部逻辑层专供包内部使用，不对业务层开放
- **操作原子性原则**：每个内部逻辑函数实现一个完整的原子操作

**架构层次设计**：
```text
┌─────────────────────────┐
│   Core Classes         │  ← 核心类层（Facade）
│   (Dataset, Project)   │    - 简单接口
└─────────────────────────┘    - 状态管理
            │                  - 转发调用
            ▼
┌─────────────────────────┐
│   Internal Layer       │  ← 内部逻辑层（Business Logic）
│   (Internal Logic)     │    - 复杂逻辑
└─────────────────────────┘    - 无状态函数
            │                  - 可重用组件
            ▼
┌─────────────────────────┐
│   Data Structures      │  ← 数据层
│   (DataFrame, Metadata)│
└─────────────────────────┘
```

**目录结构约定**：
```text
logwp/models/
├── datasets/
│   ├── base.py              # 核心Dataset类（简化）
│   └── internal/            # Dataset内部业务逻辑
│       ├── curve_operations.py    # 曲线操作逻辑
│       ├── data_operations.py     # 数据处理逻辑
│       └── validation.py          # 验证逻辑
└── internal/                # 模型层内部业务逻辑
    ├── curve_extraction.py        # 曲线提取逻辑
    ├── dataset_merge.py           # 数据集合并逻辑
    └── depth_unit_conversion.py   # 深度单位转换逻辑
```

## 5 子包详细设计

### 5.1 logwp/infra 基础工具包

**核心职责**：提供通用工具函数、GPU计算、性能监控、日志配置等基础设施。

**主要组件**：
- **GPU计算工具**：统一的CPU/GPU计算引擎，自动回退机制
- **性能监控**：内存监控、GPU监控、性能统计
- **日志配置**：结构化日志配置，JSON格式输出
- **CIIA集合类**：大小写不敏感的字典和集合
- **异步工具**：大文件I/O的异步处理工具

**关键特性**：
- **ComputeEngine**：统一计算接口，支持CPU/GPU透明切换
- **CaseInsensitiveDict/Set**：支持大小写不敏感的容器类
- **performance_monitor**：装饰器模式的性能监控
- **structlog配置**：现代化结构化日志系统

#### 5.1.1 DataFrame性能优化最佳实践

**设计背景**：在处理大规模测井数据时，pandas DataFrame的使用方式直接影响系统性能。不当的DataFrame操作会导致内存碎片化、性能警告和执行效率下降。

**核心原则**：
1. **避免逐列赋值**：防止DataFrame内部结构碎片化
2. **使用批量操作**：利用pandas内置的向量化操作
3. **预分配内存**：提前创建所需的DataFrame结构
4. **及时去碎片化**：在关键点使用copy()重建内存布局

**禁止的操作模式**：
```python
# ❌ 错误：逐列赋值导致碎片化
result_df = pd.DataFrame()
for column in data_columns:
    result_df[column] = interpolated_values[column]

# ❌ 错误：循环中逐列填充
for col in columns:
    df.loc[mask, col] = fill_value

# ❌ 错误：动态添加列
for i, new_col in enumerate(new_columns):
    df[new_col] = new_data[i]
```

**推荐的操作模式**：
```python
# ✅ 正确：使用pd.concat批量合并
available_columns = [col for col in data_columns if col in interpolated_df.columns]
if available_columns:
    interpolated_subset = interpolated_df[available_columns].copy()
    interpolated_subset.index = base_df.index
    result_df = pd.concat([base_df, interpolated_subset], axis=1)

# ✅ 正确：向量化操作批量填充
available_columns = [col for col in columns if col in df.columns]
if available_columns:
    df.loc[mask, available_columns] = fill_value

# ✅ 正确：预分配所有列
if new_columns:
    data_dict = {col: np.full(len(df), np.nan) for col in new_columns}
    new_data_df = pd.DataFrame(data_dict, index=df.index)
    result_df = pd.concat([df, new_data_df], axis=1)

# ✅ 正确：字典方式一次性创建DataFrame
interpolated_data = {}
for column in data_columns:
    # ... 处理逻辑 ...
    interpolated_data[column] = processed_values
result_df = pd.DataFrame(interpolated_data)
```

**性能优化策略**：

**1. 预分配策略**：
- 在已知需要添加多列时，预先创建所有列
- 使用字典收集数据，最后一次性创建DataFrame
- 避免在循环中动态扩展DataFrame结构

**2. 批量操作策略**：
- 优先使用pandas内置函数：`merge()`, `concat()`, `join()`
- 使用向量化操作替代Python循环
- 利用`loc`的多列赋值功能：`df.loc[mask, columns] = values`

**3. 去碎片化策略**：
- 在大量`loc`操作后使用`df.copy()`重建内存布局
- 监控性能警告，及时优化碎片化代码
- 在函数返回前确保DataFrame结构紧凑

**4. 内存管理策略**：
- 及时释放不需要的中间DataFrame
- 使用`del`显式删除大型临时变量
- 在处理大数据集时考虑分块处理

**实际应用示例**：
```python
# logwp项目中的实际优化案例
def _create_single_well_resampled_dataframe(...):
    # 预分配基础DataFrame
    new_df = pd.DataFrame({depth_column: new_depth_sequence})

    # 批量合并插值结果，避免逐列赋值
    available_columns = [col for col in data_columns if col in interpolated_df.columns]
    if available_columns:
        interpolated_subset = interpolated_df[available_columns].copy()
        interpolated_subset.index = new_df.index
        new_df = pd.concat([new_df, interpolated_subset], axis=1)

    return new_df
```

**性能监控**：
- 使用`warnings.catch_warnings()`捕获性能警告
- 监控"DataFrame is highly fragmented"警告
- 在关键函数中添加性能监控装饰器

### 5.2 logwp/models 格式无关数据模型

**核心职责**：提供格式无关的测井数据模型，包含所有业务模型组件。

#### 5.2.1 核心模型组件

**WpWellProject（聚合根）**：
- 井工程项目的组合根，管理数据一致性和完整性
- 聚合WpHead、WpWellMap和多个WpDataset
- 提供统一的数据访问接口

**WpHead（井头信息管理）**：
- 管理井的基本信息和扩展属性
- 通过ExtAttributeManager管理6种属性类别
- 支持分层属性查找机制

**WpWellMap（井名映射管理）**：
- 管理井名的标准化和映射关系
- 支持CIIA大小写不敏感
- 循环检测，防止井名映射中的循环引用

#### 5.2.2 数据集层设计

logwp包提供三种不同层次的数据集概念，满足不同场景的使用需求：

**1. WpDepthIndexedDatasetBase（完整数据集）**：
- 定义数据集的通用接口和行为
- 集成CurveMetadata管理器
- 支持三种具体类型：Continuous、Discrete、Interval
- 主要用于WpWellProject的数据管理

**完整数据集类型**：
- **WpContinuousDataset**：连续型数据集（对应WFS Continuous类型）
- **WpDiscreteDataset**：离散型数据集（对应WFS Point类型）
- **WpIntervalDataset**：区间型数据集（对应WFS Interval类型）

**2. WpDatasetBundle（轻量级数据包）**：
- 轻量级的数据传输对象（DTO），专注于数据封装
- 包含数据本身和便捷的元数据访问接口
- 支持两种具体类型：DataFrame和Array格式

**轻量级数据包类型**：
- **WpDataFrameBundle**：DataFrame数据包，适合数据预处理场景
- **WpArrayBundle**：NumPy数组数据包，适合算法计算和机器学习场景

**三层数据集架构关系**：
```text
数据集层次架构
├── 完整数据集层 (WpDepthIndexedDatasetBase)     # 项目级数据管理
│   ├── 用途: WpWellProject数据管理、完整元数据保存
│   ├── 特点: 重量级、完整功能、持久化存储
│   └── 类型: WpContinuousDataset、WpDiscreteDataset、WpIntervalDataset
└── 轻量级数据包层 (WpDatasetBundle)            # 业务层数据传输
    ├── 用途: 算法计算、数据分析、临时处理
    ├── 特点: 轻量级、高性能、便捷访问
    └── 类型: WpDataFrameBundle、WpArrayBundle
```

**设计特点**：
- **分层使用**：完整数据集用于项目管理，轻量级数据包用于业务处理
- **按需选择**：业务层根据具体需求选择合适的数据格式
- **性能优化**：轻量级数据包避免了完整数据集的创建开销
- **便捷访问**：Bundle提供井名、深度等系统信息的便捷访问方法

#### 5.2.3 轻量级数据包设计 (Bundle Architecture)

**设计目标**：为业务层和算法层提供轻量级、高性能的数据传输对象，避免完整数据集对象的创建开销。

**WpDatasetBundle（抽象基类）**：
```python
@dataclass
class WpDatasetBundle(ABC):
    # 基础属性
    name: str                                    # 数据集名称
    curve_metadata: CurveMetadata               # 完整的曲线元数据

    # 便捷属性（自动计算）
    well_curve_map: CaseInsensitiveDict         # 井名曲线映射
    is_interval_bundle: bool                    # 是否为区间数据集
    depth_curve_map: Optional[CaseInsensitiveDict]      # 单深度映射
    depth_top_curve_map: Optional[CaseInsensitiveDict]  # 顶深映射
    depth_bottom_curve_map: Optional[CaseInsensitiveDict] # 底深映射

    @property
    @abstractmethod
    def data(self) -> Any: pass                 # 数据本身（子类定义）
```

**WpDataFrameBundle（DataFrame数据包）**：
- **数据格式**：pandas DataFrame + 曲线到列名映射
- **适用场景**：数据预处理、DataFrame操作、中间处理步骤
- **特点**：保持DataFrame的灵活性，支持复杂的数据操作

**WpArrayBundle（NumPy数组数据包）**：
- **数据格式**：CaseInsensitiveDict[str, np.ndarray]
- **适用场景**：算法计算、机器学习、数值分析
- **特点**：高性能数组操作，ML库友好

**Bundle便捷属性设计**：
```python
# 自动计算的便捷属性（基于curve_metadata API）
bundle.well_curve_map          # 井名曲线映射
bundle.is_interval_bundle      # 是否为区间数据集
bundle.depth_curve_map         # 深度曲线映射（单深度情况）
bundle.depth_top_curve_map     # 顶深曲线映射（区间情况）
bundle.depth_bottom_curve_map  # 底深曲线映射（区间情况）

# 便捷访问方法
bundle.get_well_names()        # 获取井名数据
bundle.get_depths()            # 获取深度数据（单深度）
bundle.get_top_depths()        # 获取顶深数据（区间）
bundle.get_bottom_depths()     # 获取底深数据（区间）
```

**WpArrayBundle机器学习辅助方法**：
```python
# ML友好的辅助方法
bundle.to_sklearn_format(target_curve="PERM")     # 转换为(X, y)格式
bundle.get_feature_curves(exclude_system=True)    # 获取特征曲线列表
bundle.get_data_summary()                         # 获取数据统计摘要
```

**Bundle创建接口**：
```python
# 从完整数据集创建Bundle
df_bundle = dataset.extract_curve_dataframe_bundle([
    "PHIT", "GR", "T2_VALUE"
])

array_bundle = dataset.extract_curve_array_bundle([
    "PHIT", "GR", "T2_VALUE"
])
```

**Bundle架构优势**：
- **DTO模式**：纯数据传输对象，职责清晰
- **自动化**：基于curve_metadata自动计算便捷属性
- **类型安全**：明确的类型注解和抽象方法
- **大小写不敏感**：所有映射都支持CIIA架构
- **ML友好**：专门为机器学习场景优化的辅助方法

#### 5.2.4 曲线属性管理架构

**设计背景**：曲线属性管理采用分层设计，将曲线的必备核心信息（基本属性）与用户自定义的动态属性（扩展属性）完全分离，通过不同的管理器和存储机制实现。

**双层属性架构**：
```text
曲线属性管理
├── 基本属性层 (logwp/models/curve/)     # 每条曲线必备的核心信息
│   ├── CurveBasicAttributes            # 曲线基本属性封装（不可变）
│   ├── CurveMetadata                   # 曲线元数据管理器
│   └── 基本属性类型系统                 # 数据类型、类别、维数枚举
└── 扩展属性层 (WpHead.ExtAttributeManager) # 用户自定义的动态属性
    ├── 通用扩展属性管理                 # 为任意对象添加扩展属性
    ├── 6种属性类别分层查找机制           # V/WP/DS/W/C/O分层查找
    └── 曲线扩展属性                    # 曲线扩展属性只是其功能之一
```

**职责分工与关系**：

**1. 曲线基本属性 (CurveBasicAttributes)**：
- **存储位置**：数据集的CurveMetadata中
- **管理方式**：每个数据集独立管理自己的曲线基本属性
- **属性内容**：曲线名称、单位、数据类型、类别、维数、深度角色等必备信息
- **特点**：不可变对象，结构固定，每条曲线都必须具备

**2. 曲线扩展属性 (通过WpHead.ExtAttributeManager)**：
- **存储位置**：项目级别的WpHead中
- **管理方式**：项目全局统一管理，通过作用域机制定位到具体曲线
- **属性内容**：用户自定义的动态属性，如处理参数、质量标记、业务标签等
- **特点**：可选配置，结构灵活，支持复杂JSON数据

**3. WpHead扩展属性管理的通用性**：
- **设计定位**：WpHead的ExtAttributeManager是一个通用的扩展属性管理系统
- **适用对象**：可以为任意对象添加扩展属性（版本、工区、数据集、井、曲线、其它）
- **曲线扩展属性**：只是其6种属性类别中的一种（C类别）
- **其他用途**：还可以管理版本信息、工区配置、数据集参数、井信息、其它自定义属性

**使用示例**：
```python
# 1. 曲线基本属性管理（数据集级别）
project = read_wp_excel("data/test.wp.xlsx")
dataset = project.get_dataset("OBMIQ_logs")

# 访问曲线基本属性
gr_attrs = dataset.curve_metadata.get_curve("GR")
print(f"GR曲线类别: {gr_attrs.category.value}")
print(f"GR数据类型: {gr_attrs.data_type.value}")
print(f"GR曲线维数: {gr_attrs.dimension.value}")

# 2. 曲线扩展属性管理（项目级别）
# 为GR曲线添加质量控制参数
project.head.add_attribute(
    category="C",  # 曲线级别属性
    attribute="quality_flag",
    value="good",
    curve="GR",
    dataset="OBMIQ_logs",
    description="数据质量标记"
)

# 为T2_VALUE曲线添加处理参数
project.head.add_attribute(
    category="C",
    attribute="processing_params",
    value={"cutoff": 33, "bins": 50},
    curve="T2_VALUE",
    dataset="NMR_logs",
    data_type="COMP",
    description="T2处理参数"
)

# 查询曲线扩展属性
quality_flag = project.head.get_scoped_attribute_value(
    "quality_flag",
    dataset="OBMIQ_logs",
    curve="GR"
)
```

**CurveBasicAttributes（曲线基本属性）**：
```python
@dataclass(frozen=True)
class CurveBasicAttributes:
    name: WpCurveName                    # 曲线名称
    unit: str | None                     # 单位（可选）
    data_type: WpDataType               # 数据类型（INT、FLOAT、STR、BOOL）
    category: WpCurveCategory           # 曲线类别（LOGGING、COMPUTED、IDENTIFIER等）
    description: str | None             # 备注信息（可选）
    dimension: WpCurveDimension         # 维数（1D、2D）
    is_well_identifier: bool = False    # 是否井名曲线
    depth_role: WpDepthRole | None      # 深度曲线角色（SINGLE、TOP、BOTTOM）
    element_names: list[str] | None = None  # 二维组合曲线元素名称列表
```

**CurveMetadata（曲线元数据管理器）**：
- 管理数据集中所有曲线的基本属性
- CIIA支持：大小写不敏感的曲线名称查询
- 提供曲线查询、过滤、统计等功能

#### 5.2.5 WpHead通用扩展属性管理

**设计定位**：WpHead的ExtAttributeManager是一个通用的扩展属性管理系统，可以为项目中的任意对象添加扩展属性，曲线扩展属性只是其众多功能之一。

**ExtAttributeManager（通用扩展属性管理器）**：
- **通用性**：为任意对象（版本、工区、数据集、井、曲线、其它）添加扩展属性
- **分层查找**：实现6种属性类别分层查找机制（V/WP/DS/W/C/O）
- **复杂数据**：支持COMP类型的复杂JSON结构
- **作用域定位**：通过dataset、well、curve参数精确定位属性作用域

**6种属性类别系统**：
- **V（版本）**：全局唯一版本属性，项目版本信息
- **WP（工区）**：工区全局属性，整个测井工区的配置参数
- **DS（数据集）**：数据集级别属性，特定数据集的处理参数
- **W（井）**：井级别属性，特定井的地质信息、完井参数等
- **C（曲线）**：曲线级别属性，特定曲线的质量标记、处理参数等
- **O（其它）**：其它属性，不受标准作用域限制的自定义属性

**曲线扩展属性的特殊性**：
- **作用域定位**：通过dataset + curve参数精确定位到具体曲线
- **继承机制**：支持从数据集级别、井级别继承属性
- **应用场景**：质量控制标记、处理参数、业务标签、算法配置等

**COMP类型处理**：
- **StandardCompProcessor**：COMP类型JSON验证和提取
- **T2AxisProcessor**：T2轴预定义属性处理器
- **预定义属性注册机制**：支持扩展新的预定义属性类型

#### 5.2.6 WpHead扩展属性管理详细设计 (EADMA)

**设计目标**：建立统一的扩展属性管理架构，为项目中的任意对象提供扩展属性支持，实现6种属性类别的分层查找机制。

**核心特性**：
- **通用对象支持**：不仅限于曲线，可为版本、工区、数据集、井、曲线、其它对象添加扩展属性
- **格式无关设计**：完全独立于具体文件格式，支持WP、LAS、JSON等多种数据源
- **分层作用域管理**：通过dataset、well、curve参数组合实现精确的作用域定位
- **复杂数据支持**：支持字符串、数值、布尔、JSON等多种数据类型

**6种属性类别分层查找机制**：
- **V（版本）**：项目版本信息，全局唯一，如"Version": "1.0"
- **WP（工区）**：工区全局配置，如项目名称、坐标系、默认单位等
- **DS（数据集）**：数据集级别配置，如采样参数、处理算法配置等
- **W（井）**：井级别信息，如井位坐标、地质信息、完井参数等
- **C（曲线）**：曲线级别属性，如质量标记、处理参数、业务标签等
- **O（其它）**：自定义属性，不受标准作用域限制的灵活扩展

**通用扩展属性使用示例**：
```python
# 创建项目和添加各种扩展属性
project = WpWellProject(name="Santos_Field")

# 1. 版本属性（全局唯一）
project.head.add_attribute("V", "Version", "1.0")

# 2. 工区属性（项目级别配置）
project.head.add_attribute("WP", "project_name", "Santos_Field")
project.head.add_attribute("WP", "coordinate_system", "WGS84")
project.head.add_attribute("WP", "default_depth_unit", "m")

# 3. 数据集属性（数据集级别配置）
project.head.add_attribute("DS", "sampling_rate", 0.5, dataset="OBMIQ_logs")
project.head.add_attribute("DS", "T2_AXIS", t2_config, dataset="NMR_logs", data_type="COMP")

# 4. 井属性（井级别信息）
project.head.add_attribute("W", "surface_location", {"x": 123.45, "y": 67.89}, well="Well_A", data_type="COMP")
project.head.add_attribute("W", "total_depth", 3500.0, well="Well_A", unit="m")

# 5. 曲线属性（曲线级别配置）
project.head.add_attribute("C", "quality_flag", "good", dataset="OBMIQ_logs", curve="GR")
project.head.add_attribute("C", "processing_params", {"cutoff": 33}, dataset="NMR_logs", curve="T2_VALUE", data_type="COMP")

# 6. 其它属性（自定义扩展）
project.head.add_attribute("O", "custom_metadata", {"author": "John", "date": "2025-01-07"}, data_type="COMP")

# 查询属性（分层查找）
version = project.head.get_version()  # "1.0"
project_name = project.head.get_scoped_attribute_value("project_name")  # "Santos_Field"
quality_flag = project.head.get_scoped_attribute_value("quality_flag", dataset="OBMIQ_logs", curve="GR")  # "good"
```

**分层查找优先级**：
```python
def lookup_attribute(self, attribute: str, *, dataset=None, well=None, curve=None):
    # 1. 版本属性查找（最高优先级）
    if attribute == WpStandardColumn.VERSION:
        return self._lookup_version_attribute(attribute)

    # 2. 作用域属性查找（按优先级顺序）
    if any([dataset, well, curve]):
        # 2.1 曲线作用域查找（最高优先级）
        if curve:
            result = self._lookup_curve_attribute(attribute, dataset, well, curve)
            if result.found: return result

        # 2.2 井作用域查找
        if well:
            result = self._lookup_well_attribute(attribute, dataset, well)
            if result.found: return result

        # 2.3 数据集作用域查找
        if dataset:
            result = self._lookup_dataset_attribute(attribute, dataset)
            if result.found: return result

    # 3. 工区全局属性查找
    result = self._lookup_workarea_attribute(attribute)
    if result.found: return result

    # 4. 其它属性精确匹配查找
    return self._lookup_other_attribute(attribute, dataset, well, curve)
```

**COMP类型三层处理架构**：
- **第1层：JSON一般形式存储（Models层）**：直接保存完整JSON结构，具备保存任意复杂属性的能力
- **第2层：领域对象形式（业务层使用）**：强类型领域对象，编译时类型检查
- **第3层：工厂转换接口（attr层提供）**：作用域查找、工厂转换、统一接口

#### 5.2.7 曲线基本属性与扩展属性关系设计 (UCAMA)

**设计目标**：明确曲线基本属性和扩展属性的职责分工，建立清晰的管理边界和协作机制。

**双层属性架构关系**：
```text
曲线属性管理
├── 基本属性层 (数据集级别管理)          # 每条曲线必备的核心信息
│   ├── 存储位置: dataset.curve_metadata
│   ├── 管理范围: 单个数据集内的曲线
│   ├── 属性内容: 名称、单位、类型、类别、维数等
│   └── 特点: 不可变、结构固定、必备信息
└── 扩展属性层 (项目级别管理)            # 用户自定义的动态属性
    ├── 存储位置: project.head.ext_attribute_manager
    ├── 管理范围: 项目全局，通过作用域定位
    ├── 属性内容: 质量标记、处理参数、业务标签等
    └── 特点: 可变、结构灵活、可选配置
```

**职责分离原则**：
- **基本属性**：曲线的必备核心信息，每条曲线都具备，结构固定，数据集级别管理
- **扩展属性**：用户自定义的动态属性，可选配置，结构灵活，项目级别管理

**管理边界**：
- **基本属性管理**：由CurveMetadata负责，每个数据集独立管理自己的曲线基本属性
- **扩展属性管理**：由WpHead.ExtAttributeManager负责，项目全局统一管理，通过作用域机制定位到具体曲线
- **协作机制**：基本属性提供曲线的核心标识信息，扩展属性通过曲线名称和数据集名称进行关联

**CurveBasicAttributes核心设计**：
```python
@dataclass(frozen=True)
class CurveBasicAttributes:
    name: WpCurveName                    # 曲线名称
    unit: str | None                     # 单位（可选）
    data_type: WpDataType               # 数据类型（INT、FLOAT、STR、BOOL）
    category: WpCurveCategory           # 曲线类别（LOGGING、COMPUTED、IDENTIFIER等）
    description: str | None             # 备注信息（可选）
    dimension: WpCurveDimension         # 维数（1D、2D）
    is_well_identifier: bool = False    # 是否井名曲线
    depth_role: WpDepthRole | None      # 深度曲线角色（SINGLE、TOP、BOTTOM）
    element_names: list[str] | None = None  # 二维组合曲线元素名称列表
```

**CurveMetadata管理器**：
- 管理数据集中所有曲线的基本属性
- CIIA支持：大小写不敏感的曲线名称查询
- 提供曲线查询、过滤、统计等功能

#### 5.2.8 二维组合曲线管理与数据同步设计

**设计背景**：测井数据中的二维组合曲线（如T2谱、NMR数据）具有复杂的管理需求：
- **动态维度变化**：二维组合曲线的元素数量可能在数据处理过程中发生变化
- **元素级操作**：需要支持对二维组合曲线的单个元素进行删除、修改等操作
- **数据一致性**：DataFrame数据体与CurveMetadata元数据必须始终保持同步
- **自动降级需求**：当二维组合曲线的部分元素被删除后，剩余元素应自动降级为独立的一维曲线

**核心设计原则**：
- **曲线展开原则**：支持将用户提供的紧凑曲线列表展开为不同格式的完整列表
- **元素级精确控制原则**：支持对二维组合曲线的单个元素进行精确操作
- **自动降级原则**：二维组合曲线部分元素删除后，剩余元素自动降级为一维曲线
- **数据同步原则**：DataFrame与CurveMetadata的修改必须原子性同步

**三种曲线列表概念**：
1. **紧凑曲线列表（Compact Curve List）**：用户提供的输入格式
   - 示例：`['GR', 'DEN', 'T2_VALUE']`
   - 特征：二维组合曲线使用基础名称，简洁易用

2. **展开曲线列表（Expanded Curve List）**：业务逻辑使用格式
   - 示例：`['GR', 'DEN', 'T2_VALUE[1]', 'T2_VALUE[2]', 'T2_VALUE[3]', 'T2_VALUE[4]']`
   - 特征：使用`NAME[INDEX]`格式，符合业务规范

3. **DataFrame列名列表（DataFrame Column List）**：数据分析使用格式
   - 示例：`['GR', 'DEN', 'T2_VALUE_1', 'T2_VALUE_2', 'T2_VALUE_3', 'T2_VALUE_4']`
   - 特征：机器学习库友好，无特殊字符

**自动降级流程**：
```text
原始状态：
T2_VALUE (2D): ["T2_VALUE[1]", "T2_VALUE[2]", "T2_VALUE[3]", "T2_VALUE[4]"]
DataFrame: ["GR", "T2_VALUE_1", "T2_VALUE_2", "T2_VALUE_3", "T2_VALUE_4", "DEN"]

删除 T2_VALUE[2] 后：
T2_VALUE[1] (1D): dataframe_column_name="T2_VALUE_1"
T2_VALUE[3] (1D): dataframe_column_name="T2_VALUE_3"
T2_VALUE[4] (1D): dataframe_column_name="T2_VALUE_4"
DataFrame: ["GR", "T2_VALUE_1", "T2_VALUE_3", "T2_VALUE_4", "DEN"]
```

**数据同步架构**：
```python
def remove_curve(self, curve_name: str) -> None:
    # 1. 确定要删除的DataFrame列
    columns_to_remove = self._get_columns_to_remove(curve_name)

    # 2. 原子性删除操作
    new_df = self.df.drop(columns=columns_to_remove)      # 先删除DataFrame列
    self.curve_metadata.remove_curve(curve_name)          # 再删除元数据
    object.__setattr__(self, 'df', new_df)               # 同时更新
```

#### 5.2.9 曲线名称多层次架构设计

**设计背景**：在测井数据处理系统中，曲线名称存在多种表示形式，需要在不同层次间进行转换和映射。

**核心挑战**：
- **用户习惯**：用户习惯使用简洁的紧凑表达（如`T2_VALUE`表示整个二维组合曲线）
- **技术约束**：DataFrame和ML库要求标准化的列名（如`T2_VALUE_1`、`T2_VALUE_2`）
- **语义清晰**：元数据层需要保持语义明确的表达（如`T2_VALUE[1]`、`T2_VALUE[2]`）
- **查询一致性**：查询条件必须与实际DataFrame列名完全匹配

**三层名称架构**：
```text
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
│  紧凑/混合表达：["GR", "T2_VALUE", "T2_TIME[1]"]            │
│  - 日常使用习惯                                             │
│  - 简洁直观                                                 │
│  - 支持混合格式                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ 元数据管理
┌─────────────────────────────────────────────────────────────┐
│                   曲线元数据层                               │
│  分层结构：                                                 │
│  ├── "GR": CurveBasicAttributes (一维曲线对象)              │
│  ├── "T2_VALUE": CurveBasicAttributes (二维组合曲线对象)        │
│  │   └── element_names: ["T2_VALUE[1]", "T2_VALUE[2]", ...] │
│  └── "T2_TIME[1]": CurveBasicAttributes (降级一维曲线对象)  │
│  - 对象化管理                                               │
│  - 分层存储                                                 │
│  - 支持降级处理                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ dataframe_column_name属性
┌─────────────────────────────────────────────────────────────┐
│                  DataFrame数据层                            │
│  友好表达：["GR", "T2_VALUE_1", "T2_VALUE_2", "T2_TIME_1"]  │
│  - ML库兼容                                                 │
│  - 查询友好                                                 │
│  - 标准化格式                                               │
└─────────────────────────────────────────────────────────────┘
```

**名称格式规范**：
| 层次 | 存储方式 | 示例 | 用途 |
|------|---------|------|------|
| **用户层** | 紧凑格式 | `"T2_VALUE"` | 用户输入，表示整个二维组合曲线 |
| **用户层** | 混合格式 | `["GR", "T2_VALUE", "T2_TIME[1]"]` | 用户输入，混合一维、二维、元素 |
| **元数据层** | 对象分层 | `CurveBasicAttributes(name="T2_VALUE", element_names=["T2_VALUE[1]", ...])` | 对象化管理，分层存储 |
| **元数据层** | 降级对象 | `CurveBasicAttributes(name="T2_TIME[1]", is_2d=False)` | 二维元素降级为一维对象 |
| **DataFrame层** | 友好格式 | `"T2_VALUE_1"` | DataFrame列名，ML兼容 |

**双重展开设计模式**：
```python
# 核心转换流程
def extract_curves_with_metadata_and_dataframe(curve_names: list[str]) -> tuple[CurveMetadata, list[str]]:
    """元数据提取 + DataFrame列名生成。"""

    # 1. 元数据层处理（对象化管理）
    expanded_for_metadata = metadata.expand_curve_names(
        curve_names,
        CurveExpansionMode.EXPANDED
    )
    # 结果：["GR", "T2_VALUE[1]", "T2_VALUE[2]", "T2_VALUE[3]"]

    # 提取元数据对象（分层结构）
    filtered_metadata = metadata.extract_metadata(expanded_for_metadata)

    # 2. DataFrame层处理（列名生成）
    dataframe_column_names = metadata.expand_curve_names(
        curve_names,
        CurveExpansionMode.DATAFRAME
    )
    # 结果：["GR", "T2_VALUE_1", "T2_VALUE_2", "T2_VALUE_3"]

    return filtered_metadata, dataframe_column_names
```

**智能降级机制**：当用户指定二维组合曲线的特定元素时，系统自动创建独立的一维曲线对象。

```python
# 输入：混合曲线列表
user_input = ["GR", "T2_VALUE", "T2_TIME[1]", "T2_TIME[3]"]

# 元数据层处理结果（对象化分层存储）：
filtered_metadata = CurveMetadata()
filtered_metadata.curves = {
    # 一维曲线对象（直接复制）
    "GR": CurveBasicAttributes(
        name="GR",
        dimension=WpCurveDimension.ONE_D,
        dataframe_column_name="GR"
    ),

    # 完整二维组合曲线对象（包含所有元素）
    "T2_VALUE": CurveBasicAttributes(
        name="T2_VALUE",
        dimension=WpCurveDimension.TWO_D,
        element_names=["T2_VALUE[1]", "T2_VALUE[2]", ..., "T2_VALUE[50]"],
        dataframe_element_names=["T2_VALUE_1", "T2_VALUE_2", ..., "T2_VALUE_50"]
    ),

    # 降级一维曲线对象（从T2_TIME降级而来）
    "T2_TIME[1]": CurveBasicAttributes(
        name="T2_TIME[1]",
        dimension=WpCurveDimension.ONE_D,
        dataframe_column_name="T2_TIME_1"
    ),

    "T2_TIME[3]": CurveBasicAttributes(
        name="T2_TIME[3]",
        dimension=WpCurveDimension.ONE_D,
        dataframe_column_name="T2_TIME_3"
    )
}

# DataFrame层列名（从元数据对象自动生成）：
# ["GR", "T2_VALUE_1", "T2_VALUE_2", ..., "T2_VALUE_50", "T2_TIME_1", "T2_TIME_3"]
```

**架构优势**：
- **用户体验优势**：支持简化输入、智能处理、冲突检测
- **技术架构优势**：格式一致性、类型安全、扩展性强
- **系统集成优势**：无缝集成pandas DataFrame和ML库，向后兼容

### 5.3 WpWellProject核心业务方法设计

**设计背景**：WpWellProject作为聚合根，提供了测井数据处理的核心业务方法，包括曲线提取、数据集合并等功能。这些方法遵循内部业务逻辑层设计模式，将复杂业务逻辑转发给专门的内部逻辑层处理。

#### 5.3.1 曲线提取方法 (extract_curves)

**核心功能**：从指定数据集中提取曲线生成新数据集，支持条件查询和智能类型判断。

**方法签名**：
```python
def extract_curves(
    self,
    source_dataset: str,
    target_dataset: str,
    curve_names: list[str],
    query_condition: str | None = None
) -> WpDepthIndexedDatabaseBase:
```

**核心特性**：
- **紧凑曲线支持**：支持一维曲线、二维组合曲线基础名称、二维组合曲线元素的混合输入
- **自动展开机制**：`["GR", "T2_VALUE"]` → `["GR", "T2_VALUE[1]", "T2_VALUE[2]", ...]`
- **条件查询支持**：使用DataFrame.query()语法，支持`${curve_name}`占位符
- **智能类型判断**：根据深度采样特征自动判断生成数据集类型
- **WFS规范遵循**：自动包含必需的井名列和深度列

**实现流程**：
```text
1. 参数验证 → 2. 获取源数据集 → 3. 确定必需列 → 4. 曲线展开
    ↓
5. 元数据提取 → 6. 查询条件转换 → 7. 数据筛选 → 8. 智能类型判断
    ↓
9. 构造新数据集 → 10. 返回结果（不自动添加到项目）
```

**使用示例**：
```python
# 基本曲线提取
selected_dataset = project.extract_curves(
    source_dataset="OBMIQ_logs",
    target_dataset="selected_logs",
    curve_names=["GR", "DEN", "PHIT"]
)

# 带条件的曲线提取
high_gr_dataset = project.extract_curves(
    source_dataset="OBMIQ_logs",
    target_dataset="high_gr_logs",
    curve_names=["GR", "DEN", "T2_VALUE"],  # T2_VALUE自动展开
    query_condition="${GR} > 50 and ${DEN} < 2.5"
)
```

#### 5.3.2 数据集合并方法

**设计背景**：提供两种数据集合并策略，满足不同的业务需求。

##### 5.3.2.1 连续型转换合并 (merge_datasets_via_continuous)

**核心功能**：将左右数据集都转换为连续型数据集进行合并，支持重采样和统一深度采样间隔。

**方法签名**：
```python
def merge_datasets_via_continuous(
    self,
    left_dataset: str,
    left_curves: list[str],
    right_dataset: str,
    right_curves: list[str],
    *,
    left_query: str | None = None,
    right_query: str | None = None,
    merge_sampling_interval: float | None = None,
    interpolation_method: str = "nearest",
    result_mode: str = "full",
    new_dataset_name: str | None = None
) -> WpDepthIndexedDatabaseBase:
```

**核心特性**：
- **统一重采样**：将两个数据集转换为相同的深度采样间隔
- **智能结果类型**：根据深度采样特征确定最终数据集类型
- **曲线冲突处理**：自动处理重名曲线（如GR→GR_1）
- **插值方法选择**：支持智能插值方法选择架构

**适用场景**：需要统一深度采样间隔的合并场景，如机器学习特征工程。

##### 5.3.2.2 左对齐合并 (merge_datasets_left_aligned)

**核心功能**：以左数据集的深度索引作为基准进行合并，保持原始深度值。

**方法签名**：
```python
def merge_datasets_left_aligned(
    self,
    left_dataset: str,
    left_curves: list[str],
    right_dataset: str,
    right_curves: list[str],
    *,
    left_query: str | None = None,
    right_query: str | None = None,
    interpolation_method: str = "nearest",
    new_dataset_name: str | None = None
) -> WpDepthIndexedDatabaseBase:
```

**核心特性**：
- **深度保持**：保持左数据集的原始深度值和结构
- **类型保持**：最终数据集类型与左数据集相同
- **左连接模式**：以左数据集深度为准，右数据集没有对应深度点的值置空
- **智能插值**：根据曲线属性智能选择插值方法

**适用场景**：需要保持原始深度值的合并场景，如岩心数据与测井数据的融合。

#### 5.3.3 智能插值方法选择架构

**设计背景**：不同类型的曲线需要使用不同的插值方法以保证数据语义的正确性。建立三级优先级的智能插值选择机制。

**三级优先级架构**：
```text
┌─────────────────────────────────────────────────────────────┐
│                    第一级：强制插值方法                      │
│  优先级：最高 | 来源：曲线类别强制要求                       │
│  适用：CATEGORICAL曲线类型                                   │
│  方法：强制使用"nearest"                                     │
│  原因：保证类别数据的语义正确性                               │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                    第二级：数据类型约束                      │
│  优先级：中等 | 来源：WpDataType检查                         │
│  适用：STR、BOOL数据类型                                     │
│  方法：强制使用"nearest"                                     │
│  原因：字符串和布尔值无法进行数值插值                         │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                    第三级：用户指定方法                      │
│  优先级：最低 | 来源：用户参数                               │
│  适用：FLOAT、INT等数值型数据                                │
│  方法：用户指定的任意方法                                     │
│  原因：数值型数据支持多种插值策略                             │
└─────────────────────────────────────────────────────────────┘
```

**决策算法**：
```python
def get_recommended_interpolation_method(
    curve_attrs: CurveBasicAttributes,
    user_specified_method: str
) -> str:
    """智能插值方法选择的核心算法。"""

    # 第一级：检查强制插值方法
    if curve_attrs.category == WpCurveCategory.CATEGORICAL:
        return "nearest"  # 类别数据强制使用最近邻

    # 第二级：检查数据类型约束
    if curve_attrs.data_type in [WpDataType.STR, WpDataType.BOOL]:
        return "nearest"  # 字符串和布尔类型强制使用最近邻

    # 第三级：使用用户指定方法
    return user_specified_method
```

**插值方法映射表**：
| 曲线类型 | 数据类型 | 强制方法 | 技术原因 | 典型应用 |
|---------|---------|---------|---------|---------|
| **CATEGORICAL** | 任意 | `"nearest"` | 保持离散特征，避免产生不存在的类别值 | 岩性、相带、解释结论 |
| **任意** | **STR** | `"nearest"` | 字符串无法进行数值插值运算 | 注释、描述、标识 |
| **任意** | **BOOL** | `"nearest"` | 布尔值只能保持原值或最近值 | 标志位、开关状态 |
| **NORMAL** | **FLOAT/INT** | 用户指定 | 支持各种数值插值方法 | 孔隙度、渗透率、饱和度 |

**支持的插值方法**：
- `"nearest"` - 最近邻插值（最快，适合离散特征明显的数据）
- `"linear"` - 线性插值（平衡速度和精度，最常用）
- `"cubic"` - 三次样条插值（高精度，适合平滑曲线）
- `"spline"` - 样条插值（scipy.interpolate支持的其他方法）

**错误处理与回退机制**：
```python
def _perform_interpolation_with_fallback(
    source_df: pd.DataFrame,
    target_sequence: np.ndarray,
    curve_attrs: CurveBasicAttributes,
    interpolation_method: str
) -> np.ndarray:
    """带回退机制的插值执行。"""

    try:
        # 尝试执行指定的插值方法
        return _execute_interpolation(source_df, target_sequence, curve_attrs, interpolation_method)

    except ValueError as e:
        # 插值方法不支持时的回退策略
        logger.warning(f"插值方法 {interpolation_method} 失败，回退到备选方案")

        # 根据数据类型选择回退方案
        if curve_attrs.data_type == WpDataType.STR:
            return _execute_nearest_interpolation(source_df, target_sequence, curve_attrs)
        else:
            return _execute_linear_interpolation(source_df, target_sequence, curve_attrs)
```

### 5.4 logwp/io 文件读取层

**核心职责**：处理各种文件格式的读写操作，实现格式无关的数据访问。

**架构设计**：
```text
logwp/io/
├── constants.py           # I/O层格式特定常量（WpXlsxKey、WpFileFormat等）
├── exceptions.py          # I/O层异常定义（WpFileError、WpIOError等）
└── wp_excel/              # WP Excel格式支持包
    ├── wp_excel_reader.py # WP Excel主读取器（协调器）
    └── internal/          # WP Excel内部处理逻辑
        ├── curve_parser.py     # 曲线解析器
        ├── data_converter.py   # 数据转换器
        ├── dataset_parser.py   # 数据集解析器
        ├── excel_parser.py     # Excel基础解析器
        ├── head_info_parser.py # 井头信息解析器
        ├── validator.py        # 格式验证器
        └── well_map_parser.py  # 井名映射解析器
```

**主要组件**：
- **WpExcelReader**：WP Excel主读取器，协调整个读取流程，实现渐进式构造
- **internal模块群**：专门的内部处理逻辑，每个模块负责特定的解析任务
- **constants/exceptions**：I/O层专用的常量和异常定义

**设计特点**：
- **渐进式构造**：按步骤逐步构造WpWellProject对象
- **模块化解析**：每个internal模块专注特定的解析任务
- **格式解耦**：I/O层与models层严格分离
- **异常处理**：结构化异常信息和错误上下文
- **扩展性强**：易于添加新的文件格式支持（如LAS、DLIS等）

**WpExcelReader渐进式构造流程**：
1. 创建空的WpWellProject
2. 渐进式构造WpHead（井头信息）
3. 渐进式构造WpWellMap（井名映射）
4. 逐个数据集的渐进式构造
5. 返回完整项目

**internal模块职责分工**：
- **excel_parser**：Excel文件基础解析，工作表识别和数据提取
- **head_info_parser**：井头信息解析，扩展属性管理
- **well_map_parser**：井名映射解析，大小写不敏感处理
- **dataset_parser**：数据集解析，三种类型数据集的构造
- **curve_parser**：曲线解析，基本属性和二维组合曲线处理
- **data_converter**：数据转换，DataFrame友好名称生成
- **validator**：格式验证，WFS规范符合性检查

### 5.5 logwp/extras 功能扩展包（目前暂时为空）

**核心职责**：基于logwp模型提供测井方面的通用数据处理功能。

**设计定位**：
- **基于logwp模型**：使用logwp提供的数据模型和工具
- **通用性**：提供通用的测井数据处理功能，不涉及特定算法
- **可选性**：作为扩展功能，不影响logwp核心功能

**未来规划**：
- **数据预处理**：测井数据的清洗、标准化、质量控制
- **特征工程**：测井数据的特征提取和变换
- **数据融合**：多源测井数据的融合和集成
- **可视化工具**：测井数据的专业可视化功能
- **统计分析**：测井数据的统计分析和报告生成

**预留目录结构**：
```text
logwp/extras/
├── nmr/                    # NMR数据处理扩展
├── visualization/          # 可视化工具扩展
├── preprocessing/          # 数据预处理工具
├── feature_engineering/    # 特征工程工具
└── statistics/            # 统计分析工具
```

## 6 包级视图与API设计

### 6.1 logwp包核心类图

```mermaid
classDiagram
    class WpWellProject {
        +dict[str, WpDepthIndexedDatabaseBase] datasets
        +WpHead head
        +WpWellMap well_map
        +add_dataset()
        +get_dataset()
        +validate()
    }

    class WpDepthIndexedDatabaseBase {
        <<abstract>>
        +str name
        +pd.DataFrame df
        +CurveMetadata curve_metadata
        +extract_curve_dataframe_bundle()
        +extract_curve_array_bundle()
        +validate()
    }

    class WpContinuousDataset
    class WpDiscreteDataset
    class WpIntervalDataset

    class WpDatasetBundle {
        <<abstract>>
        +str name
        +CurveMetadata curve_metadata
        +CaseInsensitiveDict well_curve_map
        +bool is_interval_bundle
        +get_well_names()
        +get_depths()
    }

    class WpDataFrameBundle {
        +pd.DataFrame data
        +dict curve_to_columns_map
    }

    class WpArrayBundle {
        +CaseInsensitiveDict data
        +to_sklearn_format()
        +get_feature_curves()
    }

    class WpHead {
        +ExtAttributeManager ext_attr_manager
        +get_parsed_attribute()
        +lookup_attribute()
    }

    class WpWellMap {
        +dict[str, str] mapping
        +normalize()
        +detect_cycles()
    }

    class CurveMetadata {
        +CaseInsensitiveDict curves
        +add_curve()
        +get_curve()
        +list_curves()
    }

    class ExtAttributeManager {
        +add_attribute()
        +lookup_attribute()
        +has_attribute()
    }

    WpWellProject "1" *-- "0..*" WpDepthIndexedDatabaseBase : contains
    WpWellProject "1" *-- "1" WpHead : contains
    WpWellProject "1" *-- "1" WpWellMap : contains
    WpDepthIndexedDatabaseBase <|-- WpContinuousDataset
    WpDepthIndexedDatabaseBase <|-- WpDiscreteDataset
    WpDepthIndexedDatabaseBase <|-- WpIntervalDataset
    WpDepthIndexedDatabaseBase "1" *-- "1" CurveMetadata : contains
    WpDepthIndexedDatabaseBase ..> WpDatasetBundle : creates
    WpDatasetBundle <|-- WpDataFrameBundle
    WpDatasetBundle <|-- WpArrayBundle
    WpDatasetBundle "1" *-- "1" CurveMetadata : references
    WpHead "1" *-- "1" ExtAttributeManager : contains
```

### 6.2 包级门面API设计

```python
# 统一导入接口
import logwp

# 核心模型API（从models子包导入）
from logwp.models import WpWellProject, WpHead, WpWellMap
from logwp.models.datasets import WpContinuousDataset, WpDiscreteDataset, WpIntervalDataset
from logwp.models.datasets.bundle import WpDatasetBundle, WpDataFrameBundle, WpArrayBundle
from logwp.models.constants import WpDsType, WpDepthUnit, WpAttributeCategory
from logwp.models.exceptions import WpError, WpDataError, WpValidationError

# 曲线管理API
from logwp.models.curve import CurveBasicAttributes, CurveMetadata
from logwp.models.ext_attr import ExtAttributeManager, T2AxisProcessor

# I/O API
from logwp.io import read_wp_excel
from logwp.io.constants import WpXlsxKey, WpFileFormat
from logwp.io.exceptions import WpFileError, WpFileFormatError

# 工具API
from logwp.infra.gpu import ComputeEngine
from logwp.infra.constants import WpGpuDefaults
from logwp.infra.exceptions import WpGpuError
from logwp.infra import get_logger, performance_monitor

# 类型定义API
from logwp.models.types import WpDataDict, WpMetaDict, WpConfigDict

# 使用示例
project = WpWellProject(name="Santos_Study")
dataset = WpContinuousDataset(name="logs", df=data)
engine = ComputeEngine()
```

### 6.3 常量异常分层架构

**分层管理原则**：
```python
# 工具层
from logwp.infra.constants import WpGpuDefaults, WpComputeBackend
from logwp.infra.exceptions import WpGpuError, WpGpuMemoryError

# 模型层
from logwp.models.constants import WpDsType, WpDepthUnit, WpAttributeCategory
from logwp.models.exceptions import WpError, WpDataError, WpValidationError

# I/O层
from logwp.io.constants import WpXlsxKey, WpFileFormat
from logwp.io.exceptions import WpFileError, WpIOError

# 扩展层
from logwp.extras.constants import (扩展常量)
from logwp.extras.exceptions import (扩展异常)
```

**命名约定**：
- **常量前缀**：各层常量使用统一的Wp前缀 + 层次标识
- **异常后缀**：异常类名以Error结尾，体现异常性质
- **分组组织**：相关常量和异常按功能分组组织

## 7 架构优势与扩展性

### 7.1 架构优势

**格式独立性**：
- ✅ 支持多种数据格式，不被单一格式约束
- ✅ 业务逻辑与格式解析分离，便于单元测试
- ✅ 易于添加新格式、新算法、新功能

**性能优化**：
- ✅ 统一的数据模型便于GPU加速和并行计算
- ✅ 流式I/O支持大文件处理，内存优化
- ✅ 异步处理提升I/O性能

**开发效率**：
- ✅ 现代化类型系统，编译时错误检查
- ✅ 简化架构，避免过度工程化
- ✅ 统一工具链，提高开发效率

**可维护性**：
- ✅ 职责清晰，数据表达与业务验证分离
- ✅ 内部逻辑层模式，复杂逻辑独立测试
- ✅ 结构化日志，便于问题诊断

### 7.2 扩展性设计

**新数据格式扩展**：
- 在io层添加新的格式支持包（如las/、dlis/等）
- 每个格式包包含主读取器和internal处理逻辑
- models层保持不变，格式无关
- 通过logwp.io统一暴露读取接口

**新属性类型扩展**：
- 通过ExtAttributeManager的元数据系统扩展
- 支持新的预定义属性处理器

**新业务需求扩展**：
- 通过组合模式扩展WpWellProject
- 内部逻辑层支持新的业务逻辑

**GPU计算扩展**：
- ComputeEngine支持新的计算后端
- 自动回退机制保证兼容性

### 7.3 未来发展方向

**短期目标**：
- 完善WP Excel读取功能和internal模块
- 实现LAS文件格式支持（logwp/io/las/包）
- 优化GPU计算性能

**中期目标**：
- 扩展logwp/extras功能包
- 支持更多数据格式（DLIS、JSON、HDF5等格式包）
- 增强可视化和统计分析功能

**长期目标**：
- 构建完整的测井数据生态系统
- 支持分布式计算和云端处理
- 与主流机器学习框架深度集成

## 8 总结

logwp包作为SCAPE项目的数据管理层，成功实现了格式无关的测井数据模型设计。通过四子包架构（utils、models、io、extras）、现代化技术栈、简化设计原则和内部业务逻辑层模式，logwp包为测井数据的建模、读取、处理和扩展提供了坚实的基础。

**核心价值**：
- **格式无关**：支持多种数据格式，不被单一格式约束
- **类型安全**：现代化类型系统，编译时错误检查
- **性能优化**：GPU加速、异步I/O、内存优化
- **简化架构**：避免过度工程化，专注解决实际问题
- **扩展性强**：支持新格式、新功能的快速集成

logwp包为scape算法层和scape_case研究案例层提供了统一、高效、可靠的数据管理服务，是SCAPE项目成功的重要基石。
