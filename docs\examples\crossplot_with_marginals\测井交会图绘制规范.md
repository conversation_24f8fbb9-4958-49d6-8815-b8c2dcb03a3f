# 测井 / 油气勘探开发 / 储层评价 交会图绘制要求 (最终版)

> **目的与定位**
> 本文档旨在定义一套面向测井、油气勘探开发及储层评价领域的专业交会图（Cross-Plot）规范，并指导开发者如何将其高质量地实现。
>
> 其核心价值分为两部分：
> 1.  **实现无关的领域规范 (第1-3节):** 首先，本文档从领域专家的视角出发，定义了一套与具体绘图库（如 Matplotlib, Plotly）无关的、抽象的绘图标准。它详细规定了图件应有的结构元素、功能参数和视觉风格，构成了衡量一张交会图是否专业、规范的“度量衡”。
> 2.  **面向主流库的实现指南 (第4-5节):** 其次，本文档为开发者和AI编程助手提供了将上述抽象规范“翻译”为具体代码的详尽指导。它不仅包含了对 Matplotlib 和 Plotly 的实现映射建议，更强调了为确保图件质量必须遵守的强制性规则，并点明了在实现复杂功能（如边缘图）时必须做出的关键架构决策。
>
> 综上，本规范旨在成为连接领域需求与工程实践的桥梁，确保最终产出的交会图在科学准确性、功能完备性和视觉一致性上都达到业界领先水平。

---

## 1 图件结构与核心元素

> **说明：** 本节定义了一张标准交会图所包含的基础构成部分。它从坐标系、数据层到图例、边缘图等，完整描述了图件的“骨架”，确立了图件包含哪些核心视觉元素。

**1.1 坐标轴 (X, Y)**

* 支持 `linear / log / log10 / log2 / symlog`刻度，并允许为不同坐标轴自定义对数基 (`base`)。
* 可选择常用度量（自然对数、log10、log2）。
* 允许方向反转（如深度为 Y 轴时自上而下递增）。
* 主次刻度、网格线、轴标签、单位必填。

**1.2 主图数据层**

* 支持多 **Series**，按 `SeriesID` 区分（井号、岩性、流体相…）。
* 图元可为散点、折线或二者叠加。
* 提供 Marker 符号、尺寸、边框宽度、透明度、线宽、线型配置。
* 支持 **误差棒 (Error Bars)**，用于展示数据点在 X / Y 方向上的不确定性。

**1.3 颜色轴 (Colorbar)**

* 可对 `Z` 值做线性、对数、定性分级或离散桶化映射。
* 同步显示颜色刻度条，支持放置于左 / 右 / 顶 / 底。
* 允许多 Series 共享色轴或各自独立色轴。

**1.4 边缘图 (Marginal Plot)**

* 可启用 X 边、Y 边、双边或关闭。
* 支持图型：Histogram、CDF/CCDF、Probability Plot、KDE、Violin、Box/Boxen。
* 多 Series 叠加（Overlay）、并排（Facet）、堆叠（Stack）三种布局。
* 当主轴为对数或 symlog 时，边缘图必须使用对数等比分箱或先行对数变换。

**1.5 图例 (Legend)**

* 可选显示 Series 图例、Colorbar 图例；两者可分离。
* 位置：left / right / top / bottom / inside。

**1.6 辅助元素**

* 网格线：主 / 次网格独立开关及样式。
* 参考线：对角线。

**1.7 导出与兼容**

* 支持矢量 (SVG, PDF) 与位图 (PNG, JPG, WebP) 输出。
* 分辨率 (DPI) ≥ 300 用于出版。
* 脚本应返回 `Figure` 对象以便后续二次加工。

---

## 2 功能参数定制

> **说明：** 本节详细列出了用户可以配置的全部功能性参数，这些参数控制着图件的数据处理、行为逻辑和交互功能。**这是一个重要的架构性说明**：本节中的某些参数（如 `2.2 数据层（Series）` 中的样式设置）与第 3 节的全局外观样式存在明确分工。本节中的样式参数主要用于对**单个数据系列进行特定覆盖**，而第 3 节则定义了**全局默认主题**。关于这两类参数的详细优先级与覆盖逻辑，请务必参见 `5.1 样式参数的优先级与覆盖逻辑`，这是理解本规范架构的关键。

### 2.1 坐标轴

* `xscale`, `yscale`: `linear | log | log10 | log2 | symlog`
* `xaxis.log_base`, `yaxis.log_base`: 数值，可为 X/Y 轴分别自定义对数基数（默认 `e` / 10）
* `xlabel`, `ylabel`: 字符串，支持 LaTeX 数学渲染
* `axis.formatter`: `sci | eng | percent | plain`（科学计数、工程计数、百分比、普通）
* `axis.ticklabel.rotation`: 角度（°）
* `ticks.major.interval`, `ticks.minor.interval`, `ticks.direction`: `in|out|inout`
* `grid.major`, `grid.minor`: `on/off`, `linestyle`, `linewidth`, `alpha`
* `axis.inverted`: `True/False`

### 2.2 数据层（Series）

* `series.type` : `scatter | line | both`
* `marker.symbol`: `circle | square | triangle | star | custom-path …`
* `marker.size`: 数值或字段映射（e.g.孔隙度↔圆面积）
* `marker.facecolor`: 填充色（支持 Alpha）
* `marker.linecolor`: 边框色
* `marker.edgewidth`: 边框宽度
* `line.style`: `solid | dash | dot | dashdot`
* `line.color`: 线色（如与散点不同）
* `alpha`: 0‑1 透明度
* `zorder`: 绘制顺序
* `series.error_bars`:
    * `x.values`, `y.values`: 误差值对应的数据字段
    * `visible`: `True/False`
    * `style`: `symmetric | asymmetric`
    * `color`, `thickness`

### 2.3 颜色轴

* `cmap`: 预设色表名称或自定义列表
* `clim`: `[vmin, vmax]` 数值或自动
* `cscale`: `linear | log | quantile | category`
* `colorbar.orientation`: `vertical | horizontal`
* `colorbar.ticks`: 显式 tick 列表
* `colorbar.title`: 字符串，支持 LaTeX
* `colorbar.tickformat`: `sci | eng | plain | %.2f` 自定义格式化字符串
* `z_nan.color`: 颜色字符串 (e.g., '#cccccc')，用于指定 Z 轴数据中 NaN 值的显示颜色。若不设置则移除。

### 2.4 边缘图设置

* `marginal.enabled`: `none, x, y, both`
* `marginal.kind`: `hist, cdf, prob, kde, violin, box, boxen`
* `marginal.series`: `'all'` 或 SeriesID 列表 `['Well-A', 'Well-C']`
* `marginal.bins`: 整数或显式 bin 边界数组（支持 logspace）
* `marginal.overlay`: `overlay, facet, stack`
* `marginal.kde.bandwidth`: 数值或 `scott, silverman`
* `marginal.box.orientation`: 边缘图的方向应由其所附着的坐标轴（X轴或Y轴）自动决定，用户无需也不应手动设置

### 2.5 图例 & 画布

* `legend.show`: `True/False`
* `legend.include_colorbar`: `True/False`
* `legend.loc`: `top | bottom | left | right | inside …`
* `legend.ncols`, `legend.fontsize`
* `figure.title`: 文本（支持换行 `\n`）
* `figure.size`: `(width, height)` 英寸
* `axes.frame.visible`, `marginal.frame.visible`, `legend.frame.visible`: `True/False`
* `layout.aspect`: `auto | equal`
* `figure.watermark`:
    * `text`: 字符串 (e.g., 'Internal Use Only')
    * `visible`: `True/False`
    * `fontsize`, `color`, `alpha`, `rotation`

### 2.6 导出 & 交互

* `dpi`: 整数，默认 150
* `export.format`: `png | pdf | svg | html`
* `export.size`: `(width, height)` 英寸（若设置则 **覆盖** `figure.size` 用于导出）
* `export.transparent`: `True/False`（位图背景透明）
* `interactive.enabled`: `True/False`（Plotly 默认；Matplotlib 需 `mpld3` 或 `plt.ion()`）
* `toolbar`: `pan | zoom | reset | save`

---

## 3 外观样式定制 (默认规范)

> **说明：** 本节定义了图件的视觉外观规范。与按图件元素（如坐标轴、图例）组织参数不同，本节**按视觉属性（如字体、颜色、线型）来组织**，并为每个属性提供了完整的可定制项。这种方式的优点在于，它便于创建和应用统一的视觉主题（Theme），例如，只需修改本节中的颜色参数，即可快速切换为深色模式，确保了图件整体风格的高度一致性和可维护性。本节既是“默认样式表”，用户也可以覆盖其中任何一项来满足个性化的审美需求。所有参数均以 `style.` 为前缀。

### 3.1 字体 (Font)
* `style.font.family`: (全局字体) 字符串。**默认值: 'Arial'**
* `style.font.size_title`: (图表标题字号) 数值 (pt)。**默认值: 14**
* `style.font.weight_title`: (图表标题字重) 字符串 (`'normal'`, `'bold'`)。**默认值: 'bold'**
* `style.font.size_label`: (坐标轴标签字号) 数值 (pt)。**默认值: 12**
* `style.font.size_ticks`: (坐标轴刻度字号) 数值 (pt)。**默认值: 10**
* `style.font.color`: (全局文本颜色) 颜色字符串。**默认值: '#333333'**

### 3.2 颜色 (Color)
* `style.color.cycle`: (多系列数据颜色循环) 颜色列表名称 (`'tab10'`, `'Plotly'`) 或颜色字符串列表。**默认值: 'tab10'**
* `style.color.background_canvas`: (画布背景色) 颜色字符串。**默认值: '#ffffff'**
* `style.color.background_plot`: (主绘图区背景色) 颜色字符串。**默认值: '#ffffff'**
* `style.color.background_marginal`: (边缘图背景色) 颜色字符串。**默认值: '#f7f7f7'**
* `style.color.grid`: (网格线颜色) 颜色字符串。**默认值: '#bbbbbb'**

### 3.3 线条与边框 (Line & Frame)
* `style.linewidth.main`: (数据主线宽) 数值 (pt)。**默认值: 1.2**
* `style.linewidth.grid_major`: (主网格线宽) 数值 (pt)。**默认值: 0.6**
* `style.linewidth.grid_minor`: (次网格线宽) 数值 (pt)。**默认值: 0.4**
* `style.linewidth.reference_line`: (参考线线宽) 数值 (pt)。**默认值: 1.0**
* `style.linestyle.grid_major`: (主网格线型) 线型字符串 (`'--'`, `':'`)。**默认值: '--'`**
* `style.linestyle.grid_minor`: (次网格线型) 线型字符串。**默认值: ':'`**
* `style.linestyle.reference_line`: (参考线线型) 线型字符串。**默认值: '-.'`**
* `style.frame.color`: (绘图区边框颜色) 颜色字符串。**默认值: '#666666'**
* `style.frame.width`: (绘图区边框宽度) 数值 (pt)。**默认值: 0.8**

### 3.4 标记 (Marker)
* `style.marker.default_size`: (散点默认大小) 数值 (pt^2)。**默认值: 36**
* `style.marker.default_alpha`: (散点默认透明度) 数值 (0-1)。**默认值: 0.6**
* `style.marker.default_symbol`: (散点默认形状) 符号字符串。**默认值: 'o' (circle)**
* `style.marker.default_edgewidth`: (散点默认边框宽度) 数值 (pt)。**默认值: 0.5**

### 3.5 布局与间距 (Layout & Spacing)
* `style.padding.title`: (标题与绘图区距离) 数值 (pt)。**默认值: 10**
* `style.padding.axes_label`: (轴标签与刻度距离) 数值 (pt)。**默认值: 8**
* `style.legend.item_spacing`: (图例项间距) 数值 (pt)。**默认值: 5**
* `style.legend.handle_length`: (图例标记线长度) 数值 (pt)。**默认值: 15**

---

## 4 绘图协同与内在强制要求

> **说明：** 本节是整个规范的基石，包含了一系列为保证图件科学准确性、逻辑一致性和高性能而设定的“铁律”。这些规则不提供配置选项，任何实现都必须无条件遵守，以规避常见错误，确保图件的专业性和可靠性。

 1. **R‑1 轴‑边缘一致性**
    * 规则：主图与对应边缘图**必须共用同一 x / y 轴对象**；当主轴为对数 / symlog 时，边缘图必须使用对数等比分箱或先对数变换后在线性轴绘制，并保持 tick 对齐。
    * 违背后果：同一数值区间面积失真，cut‑off 读取错误。

 2. **R‑2 归一化柱高**
    * 规则：在对数分箱或不等宽 bin 情况下，边缘直方图柱高需按 `density=True`（Matplotlib）或 `histnorm='probability'`（Plotly）归一化，确保柱面积而非柱高代表频率。
    * 违背后果：尾部概率被夸大 / 缩小。

 3. **R‑3 Series 颜色一致**
    * 规则：边缘图各 Series 的颜色 / 线型必须与主图完全一致；禁止自动重新取色。
    * 违背后果：读者误判系列对应关系。

 4. **R‑4 legendgroup 绑定**
    * 规则：交互式图形中，主图与其 Series 级边缘图必须归属同一 `legendgroup`，以支持一键显隐。
    * 违背后果：点击图例无法同步隐藏关联元素。

 5. **R‑5 z‑order 层级**
    * 规则：主散点 / 折线层 `zorder` > 边缘图填充层 > 网格线；参考线置顶。
    * 违背后果：重要图元被遮挡或 hover 优先级错误。

 6. **R‑6 Colorbar 独立**
    * 规则：Colorbar 仅映射主图 `Z` 数据；边缘图不得复用同一 colorscale，除非边缘本身基于同一连续变量。
    * 违背后果：色轴含义混淆。

 7. **R‑7 缩放 / 导出同步**
    * 规则：任何交互缩放、坐标轴更改或导出高分辨率图像时，主图与边缘图轴域必须同时重算 limits 与 tick。
    * 违背后果：出现留白或坐标错位。

 8. **R‑8 自适应降采样**
    * 规则：当散点数量 > 5e4 或检测到渲染掉帧，脚本必须自动切换至 Hexbin / Datashader / WebGL；边缘图同步改用 KDE 概要化。
    * 违背后果：浏览器崩溃或解释软件卡顿。

 9. **R‑9 深色主题对比度**
    * 规则：重新评估 colormap 和文本/线条颜色，确保其与背景色的对比度满足 WCAG AA 级标准（对比度 ≥ 4.5:1）。
    * 违背后果：低可读性，灰阶打印后不可辨。

10. **R‑10 版本水印**
    * 规则：Figure 元数据 (`fig.meta` 或 SVG `<metadata>`) 必须嵌入规范版本号。可选通过 `figure.watermark` 在图上直接显示。
    * 违背后果：难以追溯绘图规范版本。

11. **R‑11 Tooltip 双值显示**
    * 规则：在对数或 symlog 坐标中，Hover / Tooltip 必须同时展示原值与 log 值（例如 `raw=1250, log10=3.10`）。
    * 违背后果：阅读歧义，数据解释错误。

12. **R‑12 数据清洗与无效值处理 (Data Cleaning & Invalid Values)**
    * 规则：
      * 在绘图前，必须自动检测并处理 X, Y 数据中的非有限值 (NaN, inf)。默认策略是忽略这些数据点并打印警告。可提供 `invalid_values.strategy: 'raise_error'` 选项以中断执行。
      * 对于 Z（颜色轴）中的非有限值，当 `z_nan.color` 被设置时，这些点应被赋予指定颜色；否则，它们将被忽略。
      * 程序绝不允许因无效值而崩溃。
    * 违背后果：绘图失败，或因未处理的无效值导致图形失真。

---

## 5 实现建议 (面向开发者)

> **说明：** 本节为开发者提供了实现此规范的实用建议与最佳实践，旨在指导开发者如何高效、稳健地完成编码工作，并推荐了有助于提升开发效率和代码质量的工具库。

### 5.1 核心架构原则 (Core Architectural Principles)

> **说明:** 本节阐述了构建一个健壮、可维护、可扩展的绘图系统的顶层设计思想。这些原则是后续所有实现建议的基础。

1. **配置对象模式 (Configuration Object Pattern)**
   * **思想:** 避免将海量参数作为零散变量传递。应将所有功能参数（第2节）和样式参数（第3节）聚合到一个单一的、结构化的**配置对象**中。
   * **优点:**
      * **单一数据源:** 配置对象成为绘图的唯一“真理来源”，简化了参数管理。
      * **可校验性:** 结合 Pydantic 等工具，可在运行时对整个配置进行有效性验证。
      * **可移植性:** 便于将配置序列化为 JSON/YAML，实现绘图模板的保存与加载。
   * **建议:** 强烈推荐使用 `Pydantic` 或 `dataclasses` 来定义这个配置对象。

2. **样式参数的优先级与覆盖逻辑 (Style Precedence Logic)**
   * **核心思想:** 本规范通过两类参数来控制视觉样式，它们共同构成了一套“默认主题”与“特定覆盖”相结合的灵活体系。理解它们的优先级关系是正确实现的关键。
   * **分工与区别:**
      * **第 3 节 (外观样式定制):** 定义了图件的**全局默认主题 (Global Theme)**。例如，`style.color.cycle` 为所有数据系列提供了默认的颜色循环顺序。这些参数确保了图件的整体视觉一致性。
      * **第 2.2 节 (数据层（Series）):** 提供了对**单个数据系列进行特定覆盖 (Specific Overrides)** 的能力。例如，为某个特定的 Series 设置 `marker.facecolor`，将会覆盖它从 `style.color.cycle` 中继承的默认颜色。
   * **优先级规则 (Highest to Lowest):**
      1. **颜色轴映射 (Z-Value Mapping):** 如果启用了颜色轴（`cmap`），则散点的填充色由其 `Z` 值决定，其优先级最高，会覆盖所有其他颜色设置。
      2. **数据系列特定参数 (Series-Specific Parameters):** 在 `series` 对象中直接定义的样式参数（如 `marker.facecolor`, `line.style`）拥有次高优先级。它用于对某个特定系列进行个性化设置。
      3. **全局样式默认值 (Global Style Defaults):** 第 3 节中定义的 `style.*` 参数优先级最低。它们仅在更高优先级的参数未被设置时生效，充当“兜底”的默认值。
   * **实现示例:**
      * 当绘制一个 Series 时，程序应首先检查该 Series 是否有自己的 `marker.facecolor` 设置。
      * 如果有，则使用它。
      * 如果没有，程序再从 `style.color.cycle` 颜色循环列表中按顺序取一个颜色作为其默认颜色。
      * (如果启用了颜色轴，则上述两步对于填充色均不适用，颜色由Z值决定)。

3. **可扩展的组件注册机制 (Extensible Component Registration)**
   * **思想:** 避免使用冗长的 `if/elif/else` 结构来处理不同类型的图件（如 `marginal.kind`）。应采用更灵活的**注册/工厂模式**。
   * **优点:** 实现“对扩展开放，对修改关闭”。未来新增一种边缘图类型时，只需编写一个新的绘图函数并将其注册，无需修改核心的渲染逻辑。
   * **建议:** 维护一个字典（注册表），将 `kind` 的名称映射到对应的绘图函数。例如：`marginal_drawers = {'hist': draw_histogram, 'kde': draw_kde, ...}`。

### 5.2 具体实现指南 (Specific Implementation Guides)

1. **样式翻译层 (Style Adapter)**：本规范中的 `style` 参数是一套**抽象的、与具体绘图库无关的规范**。开发者需要创建一个“翻译层”或“适配器”，将这些抽象参数转换为特定库（如 Matplotlib 或 Plotly）的原生设置。例如，一个适配器在接收到 `style.color.background_plot` 时，应能将其分别翻译为 Matplotlib 的 `ax.set_facecolor()` 或 Plotly 的 `fig.update_layout(plot_bgcolor=...)`。这种解耦设计便于实现统一的**主题化功能**和**跨库的视觉一致性**。
2. **核心功能映射指南 (Mapping Guide)**：
   * **核心思想：** 规范中的许多参数在绘图库中有直接对应，但部分核心结构（如边缘图）是需要通过组合库的多个功能来实现的复合结构。
   * **通用实现模式：**
      1. **结构先行：** 在绘图之前，需要先构建好图件的整体布局，特别是包含边缘图的复杂结构。
      2. **数据驱动循环：** 核心绘图逻辑应围绕数据中的 `Series` 列表进行循环。在每次循环中，根据当前 Series 的参数（如 `type`, `marker`, `line`）调用相应的绘图函数。
      3. **配置后置：** 在数据绘制完成后，统一应用坐标轴、图例、标题等全局配置。
   * **Matplotlib 实现要点：**
      * **图件结构：** 推荐使用 `matplotlib.gridspec.GridSpec` 来精确控制主图与边缘图的位置和比例。通过设置共享坐标轴 (`sharex`, `sharey`) 来确保联动。
      * **参数映射示例：** `xlabel` -> `ax.set_xlabel()`; `xscale: 'log'` -> `ax.set_xscale('log')`; `series.error_bars` -> `ax.errorbar()`.
   * **Plotly 实现要点：**
      * **图件结构：** `plotly.express` (px) 提供了最便捷的边缘图实现方式 (`px.scatter(..., marginal_x="histogram")`)。若需深度定制，则需使用 `plotly.graph_objects` (go) 和 `make_subplots`。
      * **多 Series:** 在 `plotly.express` 中，可通过 `color` 参数自动处理；在使用 `graph_objects` 时，需要 `for series in data: fig.add_trace(go.Scatter(...))` 循环添加。
      * **参数映射示例：** `xlabel` -> `fig.update_layout(xaxis_title=...)`; `xscale: 'log'` -> `fig.update_layout(xaxis_type='log')`; `series.error_bars` -> `go.Scatter(error_x=dict(type='data', ...))`.
3. **关键架构决策：Plotly 实现策略 (CRITICAL ARCHITECTURE DECISION)**
   * **问题背景:** `plotly.express` 的 `marginal_x`/`marginal_y` 参数在主轴为对数刻度时，无法自动进行正确的对数分箱，会导致边缘图失真，违反本规范的核心要求。
   * **架构决策:** 为确保完全符合本规范（特别是 `R-1`, `R-2`），并支持所有复杂的边缘图类型和多系列叠加，**Plotly 的实现架构必须从一开始就基于 `plotly.graph_objects` 和 `make_subplots`**。
   * **指导原则:**
      * **禁止将 `plotly.express` 作为主要实现方案。** 它可以作为内部辅助工具（例如，用于快速计算 KDE 数据），但绝不能用于构建最终的、包含边缘图的 Figure 对象。
      * **必须手动创建子图布局**，并精确控制每个子图（主图、边缘X图、边缘Y图）的 `domain`、`row`、`col` 属性。
      * **必须手动管理坐标轴的共享与同步**，确保边缘图与主图的精确联动。
      * 做出此项决策是为了保证最高程度的灵活性、可扩展性和对规范的忠实度，避免在开发中途因高层 API 的局限性而被迫重构。

