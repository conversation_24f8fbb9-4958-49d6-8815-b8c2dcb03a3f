# 1. Plotly 6.0 原生功能支持分析 (Plotly 6.0 Native Feature Analysis)

> **说明：** 本节基于 Plotly 6.0 的原生功能和我们在 `crossplot_plotly6_augment.py` 中的实际编程实践，详细分析了《测井交会图绘制要求》第1-4节中各项功能的支持情况。这为开发者提供了明确的实现路径和技术边界。

## 1.1 完全支持的功能 (✅ Fully Supported)

**坐标轴功能 (第1.1节, 第2.1节)**
- ✅ **对数坐标轴**：`fig.update_xaxes(type="log")` 原生支持，包括 log10 基数
- ✅ **线性坐标轴**：默认支持，性能优异
- ✅ **坐标轴范围设置**：`range=[min, max]` 支持，对数轴需使用指数值
- ✅ **主次刻度控制**：`minor=dict(ticks="inside", ticklen=6, showgrid=True)` 完整支持
- ✅ **网格线样式**：主次网格线独立控制，支持颜色、宽度、样式
- ✅ **轴标签和格式化**：支持 LaTeX 渲染和自定义格式化

**边缘图功能 (第1.4节, 第2.4节)**
- ✅ **多种边缘图类型**：histogram, box, violin, kde (基于 violin 实现)
- ✅ **边缘图高级配置**：Plotly 6.0 原生支持箱线图数据点显示、小提琴图内部箱线图等
- ✅ **颜色联动**：边缘图与主图图例完全同步，支持交互式显隐
- ✅ **尺寸和间距控制**：通过 `make_subplots` 精确控制边缘图占比

**颜色轴功能 (第1.3节, 第2.3节)**
- ✅ **多系列独立色轴**：每个系列可配置独立的 `colorbar`，支持不同物理量同时可视化
- ✅ **色轴位置控制**：`x`, `y`, `xanchor`, `yanchor` 精确定位，支持垂直居中
- ✅ **颜色映射**：支持所有 Plotly 内置 colorscale，自定义颜色映射
- ✅ **色轴标题和刻度**：完整的标题、刻度值、刻度文本自定义

**数据层功能 (第1.2节, 第2.2节)**
- ✅ **多系列支持**：通过 `legendgroup` 实现系列分组和联动
- ✅ **散点和折线**：`go.Scatter` 支持 markers, lines, 或两者组合
- ✅ **误差棒**：`error_x`, `error_y` 完整支持对称和非对称误差
- ✅ **标记符号和样式**：丰富的 marker 符号、尺寸、透明度、边框控制

**图例和布局 (第1.5节, 第2.5节)**
- ✅ **图例位置控制**：支持所有位置选项，智能避让色轴
- ✅ **图件尺寸和标题**：完整的布局控制，支持响应式设计
- ✅ **导出格式**：PNG, PDF, SVG, HTML 全支持，DPI 可控

## 1.2 部分支持或需要变通的功能 (⚠️ Partially Supported)

**坐标轴限制**
- ⚠️ **symlog 坐标轴**：Plotly 6.0 不支持 symlog（对称对数）刻度

- ⚠️ **自定义对数基数**：仅支持 log10，不支持 log2 或自然对数


**边缘图限制**
- ⚠️ **CDF/CCDF 边缘图**：需要手动计算累积分布函数

- ⚠️ **Probability Plot**：需要使用 `scipy.stats` 计算概率图数据

- ⚠️ **对数轴边缘图分箱**：需要手动实现对数等比分箱
  - **实现方案**：使用 `np.logspace()` 创建对数分箱，确保与主轴一致

## 1.3 不支持或需要完全自定义的功能 (❌ Not Supported)

**高级坐标轴功能**
- ❌ **坐标轴方向反转**：虽然可以通过 `autorange="reversed"` 实现，但与边缘图联动复杂
  - **影响**：深度轴自上而下递增等地质应用场景需要额外处理

**复杂边缘图布局**
- ❌ **边缘图 Facet 模式**：Plotly 原生不支持边缘图的分面显示
  - **影响**：多系列并排显示需要手动实现子图布局

- ❌ **边缘图 Stack 模式**：堆叠显示多系列边缘图需要手动计算
  - **影响**：无法直接实现多系列堆叠直方图

# 2. 代码实现最佳实践

**架构选择**
1. **强制使用 `plotly.graph_objects` + `make_subplots`**：确保完全控制和规范遵循
2. **避免 `plotly.express` 作为主要方案**：仅用作数据计算的辅助工具
3. **分层架构设计**：配置层 → 翻译层 → 绘图层 → 输出层

**性能优化**
1. **大数据处理**：超过 5万点自动切换到 WebGL 渲染 (`go.Scattergl`)
2. **边缘图优化**：大数据集使用 KDE 替代直方图，避免过多 bins
3. **内存管理**：及时释放中间数据，使用生成器处理大数据集

**兼容性保证**
1. **浏览器兼容**：确保生成的 HTML 在主流浏览器中正常显示
2. **导出质量**：PNG/PDF 导出使用 300+ DPI，确保出版质量
3. **交互功能**：充分利用 Plotly 的交互特性，但提供静态导出选项

**开发工具链**
1. **类型安全**：使用 Pydantic 进行配置验证，TypedDict 定义数据结构


# 3. 与规范的对应关系

| 规范要求 | Plotly 6.0 支持度 | 实现复杂度 | 推荐方案 |
|---------|------------------|-----------|---------|
| R-1 轴-边缘一致性 | ✅ 完全支持 | 中等 | `make_subplots` + 共享轴 |
| R-2 归一化柱高 | ✅ 完全支持 | 简单 | `histnorm='probability'` |
| R-3 Series 颜色一致 | ✅ 完全支持 | 简单 | `legendgroup` 绑定 |
| R-4 legendgroup 绑定 | ✅ 完全支持 | 简单 | 原生 `legendgroup` 属性 |
| R-5 z-order 层级 | ✅ 完全支持 | 简单 | trace 添加顺序控制 |
| R-6 Colorbar 独立 | ✅ 完全支持 | 中等 | 多 `coloraxis` 配置 |
| R-7 缩放/导出同步 | ✅ 完全支持 | 简单 | Plotly 自动处理 |
| R-8 自适应降采样 | ⚠️ 需要手动实现 | 高 | 数据量检测 + WebGL |
| R-9 深色主题对比度 | ✅ 完全支持 | 中等 | 模板系统 + 颜色验证 |
| R-10 版本水印 | ✅ 完全支持 | 简单 | `fig.add_annotation()` |
| R-11 Tooltip 双值显示 | ✅ 完全支持 | 中等 | 自定义 `hovertemplate` |
| R-12 数据清洗 | ✅ 完全支持 | 简单 | 数据预处理 + 异常处理 |

**总结**：Plotly 6.0 对《测井交会图绘制要求》的支持度约为 **85%**，核心功能完全支持，部分高级功能需要变通实现。通过合理的架构设计和实现策略，可以达到规范要求的 **95%** 以上的功能覆盖。

# 4. 参数映射表 (Parameter Mapping Table)

> **说明：** 本节提供了《测井交会图绘制要求》第2节（功能参数定义）和第3节（外观样式定制）中的参数与 `crossplot_plotly6_augment.py` 实现的详细对应关系。这为开发者提供了从规范到代码的直接映射指南。

通过这个详细的参数映射表，开发者可以：
1. **快速定位**：从规范参数直接找到对应的实现代码
2. **理解架构**：掌握配置对象模式和优先级逻辑
3. **处理限制**：了解Plotly限制和相应的变通方案
4. **扩展功能**：按照既定模式添加新功能
5. **保持一致**：确保实现与规范的完全对应

## 4.1 功能参数映射 (第2节 → 实现代码)

| 规范参数 | 实现类/属性 | 对应代码位置 | 说明 |
|---------|------------|-------------|------|
| **2.1 坐标轴设置** |
| `xlabel`, `ylabel` | `AxisConfig.title.text` | `AxisTitleConfig.text` | 轴标题文本 |
| `xscale`, `yscale` | `AxisConfig.scale` | `ScaleType.LINEAR/LOG` | 坐标轴类型 |
| `xlim`, `ylim` | `AxisConfig.range` | `AxisRangeConfig.min_value/max_value` | 坐标轴范围 |
| `xticks`, `yticks` | `AxisConfig.ticks` | `TickConfig.interval/start` | 刻度设置 |
| `xgrid`, `ygrid` | `AxisConfig.grid` | `GridConfig.major_enabled/minor_enabled` | 网格线控制 |
| **2.2 数据层（Series）** |
| `series.id` | `SeriesConfig.id` | `SeriesConfig.id` | 系列标识符 |
| `series.type` | `SeriesConfig.type` | `SeriesType.SCATTER/LINE` | 绘图类型 |
| `series.marker.symbol` | `SeriesConfig.marker.symbol` | `MarkerConfig.symbol` | 标记符号 |
| `series.marker.size` | `SeriesConfig.marker.size` | `MarkerConfig.size` | 标记大小 |
| `series.marker.facecolor` | `SeriesConfig.marker.facecolor` | `MarkerConfig.facecolor` | 填充色 |
| `series.marker.edgecolor` | `SeriesConfig.marker.linecolor` | `MarkerConfig.linecolor` | 边框色 |
| `series.marker.edgewidth` | `SeriesConfig.marker.edgewidth` | `MarkerConfig.edgewidth` | 边框宽度 |
| `series.marker.alpha` | `SeriesConfig.marker.opacity` | `MarkerConfig.opacity` | 透明度 |
| `series.line.style` | `SeriesConfig.line.style` | `LineConfig.style` | 线型 |
| `series.line.color` | `SeriesConfig.line.color` | `LineConfig.color` | 线条颜色 |
| `series.line.width` | `SeriesConfig.line.width` | `LineConfig.width` | 线条宽度 |
| `series.error_bars` | `SeriesConfig.error_bars` | `ErrorBarConfig.*` | 误差棒配置 |
| `series.hover` | `SeriesConfig.hover` | `HoverConfig.*` | 悬停信息 |
| `series.legendgroup` | `SeriesConfig.legendgroup` | `SeriesConfig.legendgroup` | 图例分组 |
| **2.3 颜色轴设置** |
| `cmap` | `ColorbarConfig.cmap` | `ColorbarConfig.cmap` | 颜色映射 |
| `clim` | `ColorbarConfig.clim` | `ColorbarConfig.clim` | 颜色范围 |
| `cscale` | `ColorbarConfig.scale` | `ColorbarConfig.scale` | 颜色刻度类型 |
| `colorbar.orientation` | `ColorbarConfig.orientation` | `ColorbarConfig.orientation` | 色轴方向 |
| `colorbar.title` | `ColorbarConfig.title` | `ColorbarConfig.title` | 色轴标题 |
| `colorbar.ticks` | `ColorbarConfig.tickvals` | `ColorbarConfig.tickvals` | 色轴刻度 |
| `z_nan.color` | `ColorbarConfig.nan_color` | `ColorbarConfig.nan_color` | NaN值颜色 |
| **2.4 边缘图设置** |
| `marginal.enabled` | `MarginalConfig.enabled` | `MarginalEnabled.NONE/X/Y/BOTH` | 边缘图启用 |
| `marginal.kind` | `MarginalConfig.kind` | `MarginalKind.HIST/BOX/VIOLIN/KDE` | 边缘图类型 |
| `marginal.series` | `MarginalConfig.series` | `MarginalConfig.series` | 系列选择 |
| `marginal.bins` | `MarginalConfig.bins` | `MarginalConfig.bins` | 分箱设置 |
| `marginal.overlay` | `MarginalConfig.overlay` | `MarginalConfig.overlay` | 叠加模式 |
| `marginal.kde.bandwidth` | `KDEStyleConfig.bandwidth` | `KDEStyleConfig.bandwidth` | KDE带宽 |
| `marginal.box.orientation` | 自动推导 | `_draw_marginal_*` 方法 | 自动确定方向 |
| **2.5 图例 & 画布** |
| `legend.show` | `LegendConfig.enabled` | `LegendConfig.enabled` | 图例显示 |
| `legend.loc` | `LegendConfig.position` | `LegendConfig.position` | 图例位置 |
| `legend.ncols` | `LegendConfig.ncols` | `LegendConfig.ncols` | 图例列数 |
| `legend.fontsize` | `style.font.size_legend` | `StyleConfig.font.size_legend` | 图例字号 |
| `figure.title` | `FigureConfig.title` | `FigureConfig.title` | 图表标题 |
| `figure.size` | `FigureConfig.size` | `FigureConfig.size` | 图表尺寸 |
| `figure.watermark` | `FigureConfig.watermark` | `FigureConfig.watermark` | 水印设置 |
| `layout.aspect` | `FigureConfig.aspect` | `FigureConfig.aspect` | 纵横比 |
| **2.6 导出 & 交互** |
| `dpi` | `save_crossplot()` 参数 | `save_crossplot(dpi=300)` | 导出分辨率 |
| `export.format` | `save_crossplot()` 参数 | 文件扩展名自动识别 | 导出格式 |
| `export.size` | `save_crossplot()` 参数 | `save_crossplot(width, height)` | 导出尺寸 |
| `interactive.enabled` | Plotly 默认支持 | 原生交互功能 | 交互模式 |

## 4.2 样式参数映射 (第3节 → 实现代码)

| 规范参数 | 实现类/属性 | 对应代码位置 | 默认值 |
|---------|------------|-------------|--------|
| **3.1 字体 (Font)** |
| `style.font.family` | `StyleConfig.font.family` | `StyleConfig.font["family"]` | "Arial" |
| `style.font.size_title` | `StyleConfig.font.size_title` | `StyleConfig.font["size_title"]` | 14 |
| `style.font.weight_title` | `StyleConfig.font.weight_title` | `StyleConfig.font["weight_title"]` | "bold" |
| `style.font.size_label` | `StyleConfig.font.size_label` | `StyleConfig.font["size_label"]` | 12 |
| `style.font.size_ticks` | `StyleConfig.font.size_ticks` | `StyleConfig.font["size_ticks"]` | 10 |
| `style.font.color` | `StyleConfig.font.color` | `StyleConfig.font["color"]` | "#333333" |
| **3.2 颜色 (Color)** |
| `style.color.cycle` | `StyleConfig.color.cycle` | `StyleConfig.color["cycle"]` | ["#1f77b4", "#ff7f0e", ...] |
| `style.color.background_canvas` | `StyleConfig.color.background_canvas` | `StyleConfig.color["background_canvas"]` | "#ffffff" |
| `style.color.background_plot` | `StyleConfig.color.background_plot` | `StyleConfig.color["background_plot"]` | "#ffffff" |
| `style.color.background_marginal` | `StyleConfig.color.background_marginal` | `StyleConfig.color["background_marginal"]` | "#f7f7f7" |
| `style.color.grid` | `StyleConfig.color.grid` | `StyleConfig.color["grid"]` | "#bbbbbb" |
| **3.3 线条与边框 (Line & Frame)** |
| `style.linewidth.main` | `StyleConfig.linewidth.main` | `StyleConfig.linewidth["main"]` | 1.2 |
| `style.linewidth.grid_major` | `StyleConfig.linewidth.grid_major` | `StyleConfig.linewidth["grid_major"]` | 0.6 |
| `style.linewidth.grid_minor` | `StyleConfig.linewidth.grid_minor` | `StyleConfig.linewidth["grid_minor"]` | 0.4 |
| `style.linewidth.reference_line` | `StyleConfig.linewidth.reference_line` | `StyleConfig.linewidth["reference_line"]` | 1.0 |
| `style.linestyle.grid_major` | `StyleConfig.linestyle.grid_major` | `StyleConfig.linestyle["grid_major"]` | "--" |
| `style.linestyle.grid_minor` | `StyleConfig.linestyle.grid_minor` | `StyleConfig.linestyle["grid_minor"]` | ":" |
| `style.linestyle.reference_line` | `StyleConfig.linestyle.reference_line` | `StyleConfig.linestyle["reference_line"]` | "-." |
| `style.frame.color` | `StyleConfig.frame.color` | `StyleConfig.frame["color"]` | "#666666" |
| `style.frame.width` | `StyleConfig.frame.width` | `StyleConfig.frame["width"]` | 0.8 |
| **3.4 标记 (Marker)** |
| `style.marker.default_size` | `StyleConfig.marker.default_size` | `StyleConfig.marker["default_size"]` | 36 |
| `style.marker.default_alpha` | `StyleConfig.marker.default_alpha` | `StyleConfig.marker["default_alpha"]` | 0.6 |
| `style.marker.default_symbol` | `StyleConfig.marker.default_symbol` | `StyleConfig.marker["default_symbol"]` | "circle" |
| `style.marker.default_edgewidth` | `StyleConfig.marker.default_edgewidth` | `StyleConfig.marker["default_edgewidth"]` | 0.5 |
| **3.5 布局与间距 (Layout & Spacing)** |
| `style.padding.title` | `StyleConfig.padding.title` | `StyleConfig.padding["title"]` | 10 |
| `style.padding.axes_label` | `StyleConfig.padding.axes_label` | `StyleConfig.padding["axes_label"]` | 8 |
| `style.legend.item_spacing` | `StyleConfig.legend.item_spacing` | `StyleConfig.legend["item_spacing"]` | 5 |
| `style.legend.handle_length` | `StyleConfig.legend.handle_length` | `StyleConfig.legend["handle_length"]` | 15 |

## 4.3 实现架构映射 (Implementation Architecture Mapping)

| 规范概念 | 实现方式 | 核心类/方法 | 说明 |
|---------|---------|------------|------|
| **配置对象模式** | Pydantic BaseModel | `CrossPlotConfig` | 单一配置入口，类型安全验证 |
| **样式优先级逻辑** | 方法内优先级检查 | `_get_color()`, `_draw_main_trace()` | Z值映射 > 系列特定 > 全局默认 |
| **组件注册机制** | 字典映射 + 方法分发 | `marginal_drawers` 字典 | 可扩展的边缘图类型支持 |
| **边缘图-主图联动** | 共享轴对象 | `make_subplots()` + 轴同步 | 确保坐标轴一致性 |
| **对数分箱处理** | 手动计算对数分箱 | `_calculate_log_bins()` | 解决Plotly Express限制 |
| **多系列颜色一致性** | `legendgroup` 绑定 | `SeriesConfig.legendgroup` | 主图与边缘图颜色同步 |
| **双色轴支持** | 独立colorbar配置 | `SeriesConfig.colorbar` | 每个系列独立色轴 |
| **性能优化** | 自动WebGL切换 | 数据量检测逻辑 | 大数据集自动优化 |

## 4.4 未实现功能

以下功能并未实现，只是是供一些可能的途径：

| 规范要求 | 实现挑战 | 变通方案 | 代码位置 |
|---------|---------|---------|---------|
| **symlog坐标轴** | Plotly不支持 | 数据预处理 + 自定义刻度 | 建议在数据层处理 |
| **自定义对数基数** | 仅支持log10 | 数据变换 + 刻度标签 | `np.log2(data)` + `ticktext` |
| **边缘图Facet模式** | 原生不支持 | 手动子图布局 | `make_subplots` 扩展 |
| **CDF/CCDF边缘图** | 需手动计算 | `np.cumsum()` + `go.Scatter` | 自定义绘图函数 |
| **对数轴边缘图分箱** | Express API限制 | 手动对数分箱 | `_calculate_log_bins()` |
| **深度轴反转** | 与边缘图联动复杂 | 数据层反转 | 数据预处理阶段 |
| **版本水印** | 需手动实现 | `fig.add_annotation()` | `FigureConfig.watermark` |
| **Tooltip双值显示** | 需自定义模板 | `hovertemplate` 定制 | `HoverConfig.template` |

## 4.5 配置示例对照 (Configuration Examples)

**规范配置示例：**
```python
# 规范中的参数设置
config = {
    "xlabel": "Porosity (%)",
    "xscale": "linear",
    "series": [{
        "id": "Well-A",
        "marker": {"symbol": "circle", "size": 8, "facecolor": "#1f77b4"}
    }],
    "marginal": {"enabled": "both", "kind": "hist"},
    "style": {"font": {"family": "Arial", "size_title": 14}}
}
```

**对应的实现配置：**
```python
# crossplot_plotly6_augment.py 中的配置
config = CrossPlotConfig(
    xaxis=AxisConfig(
        title=AxisTitleConfig(text="Porosity (%)"),
        scale=ScaleType.LINEAR
    ),
    series=[SeriesConfig(
        id="Well-A",
        marker=MarkerConfig(
            symbol="circle",
            size=8,
            facecolor="#1f77b4"
        )
    )],
    marginal=MarginalConfig(
        enabled=MarginalEnabled.BOTH,
        kind=MarginalKind.HIST
    ),
    style=StyleConfig(
        font={"family": "Arial", "size_title": 14}
    )
)
```

## 4.6 扩展性指南 (Extensibility Guidelines)

| 扩展需求 | 实现方法 | 示例代码 |
|---------|---------|---------|
| **新增边缘图类型** | 注册新的绘图函数 | `marginal_drawers['newtype'] = draw_newtype` |
| **新增标记符号** | 扩展MarkerConfig | `symbol: Literal["circle", "square", "custom"]` |
| **新增颜色映射** | 扩展ColorbarConfig | `cmap: Literal["Viridis", "Plasma", "Custom"]` |
| **新增导出格式** | 扩展save_crossplot | `format: Literal["png", "pdf", "webp"]` |
| **新增交互功能** | 扩展HoverConfig | 添加新的hover模式和模板 |

