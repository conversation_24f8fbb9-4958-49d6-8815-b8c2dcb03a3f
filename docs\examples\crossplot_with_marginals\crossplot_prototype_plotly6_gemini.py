# -*- coding: utf-8 -*-
"""
Cross-Plot Prototype using Plotly 6.0+

This script serves as a rapid prototype to demonstrate the feasibility and
implementation of the detailed cross-plot specification document.

It strictly follows the architectural decisions outlined in the prompt,
particularly the use of `plotly.graph_objects` and `make_subplots`
to ensure full compliance with the specification's requirements.
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
import itertools
from typing import List, Dict, Any
import os # Import os module to handle file paths

# ==============================================================================
# Section: Input Data Generation
# As per the prompt, we generate random but meaningful sample data.
# ==============================================================================

def generate_sample_data(num_points_per_series: int = 150) -> List[Dict[str, Any]]:
    """
    Generates a list of sample data points for two series.

    Args:
        num_points_per_series: The number of points to generate for each series.

    Returns:
        A list of dictionaries, where each dictionary represents a data point.
    """
    data = []
    np.random.seed(42) # for reproducibility

    # --- Series '岩性A' ---
    x_a = np.random.lognormal(mean=2, sigma=0.5, size=num_points_per_series) * 10
    y_a = 2 * x_a + np.random.normal(loc=0, scale=25, size=num_points_per_series)
    z_a = np.random.uniform(1800, 2200, size=num_points_per_series)
    error_x_a = x_a * np.random.uniform(0.05, 0.15, size=num_points_per_series)
    error_y_a = y_a * np.random.uniform(0.05, 0.1, size=num_points_per_series)

    for i in range(num_points_per_series):
        data.append({
            'x': x_a[i],
            'y': y_a[i],
            'z': z_a[i],
            'error_x': error_x_a[i],
            'error_y': error_y_a[i],
            'series_id': '岩性A'
        })

    # --- Series '岩性B' ---
    x_b = np.random.lognormal(mean=2.5, sigma=0.6, size=num_points_per_series) * 10
    y_b = 1.5 * x_b + np.random.normal(loc=50, scale=30, size=num_points_per_series)
    z_b = np.random.uniform(2000, 2500, size=num_points_per_series)
    error_x_b = x_b * np.random.uniform(0.05, 0.2, size=num_points_per_series)
    error_y_b = y_b * np.random.uniform(0.05, 0.12, size=num_points_per_series)

    for i in range(num_points_per_series):
        data.append({
            'x': x_b[i],
            'y': y_b[i],
            'z': z_b[i],
            'error_x': error_x_b[i],
            'error_y': error_y_b[i],
            'series_id': '岩性B'
        })

    return data


# ==============================================================================
# Section: Main Plotting Function
# Implements the `create_crossplot` function as required.
# ==============================================================================

def create_crossplot(config: Dict[str, Any], data: List[Dict[str, Any]]) -> go.Figure:
    """
    Creates a cross-plot figure based on a detailed configuration object.

    Args:
        config: A dictionary following the structure of Sections 2 & 3 of the spec.
        data: A list of data point dictionaries.

    Returns:
        A plotly.graph_objects.Figure object.
    """
    # --- Architecture: Section 5.2, item 7 ---
    # The entire figure is built using `make_subplots` to handle the main plot
    # and optional marginal plots correctly. This is a critical architectural decision.
    has_marginal_x = config.get('marginal', {}).get('enabled') in ['x', 'both']
    has_marginal_y = config.get('marginal', {}).get('enabled') in ['y', 'both']

    # Define subplot layout
    if has_marginal_x and has_marginal_y:
        fig = make_subplots(
            rows=2, cols=2,
            column_widths=[0.8, 0.2], row_heights=[0.2, 0.8],
            specs=[[{"type": "xy"}, {"type": "xy"}],
                   [{"type": "scatter"}, {"type": "xy"}]],
            horizontal_spacing=0.01, vertical_spacing=0.01,
            shared_xaxes='columns', shared_yaxes='rows'
        )
    elif has_marginal_x:
        fig = make_subplots(
            rows=2, cols=1, row_heights=[0.2, 0.8],
            specs=[[{"type": "xy"}], [{"type": "scatter"}]],
            vertical_spacing=0.01, shared_xaxes=True
        )
    elif has_marginal_y:
        fig = make_subplots(
            rows=1, cols=2, column_widths=[0.8, 0.2],
            specs=[[{"type": "scatter"}, {"type": "xy"}]],
            horizontal_spacing=0.01, shared_yaxes=True
        )
    else:
        fig = make_subplots(rows=1, cols=1, specs=[[{"type": "scatter"}]])

    # --- Group data by series_id ---
    data_by_series = {}
    for point in data:
        series_id = point['series_id']
        if series_id not in data_by_series:
            data_by_series[series_id] = []
        data_by_series[series_id].append(point)

    # --- Section 3: Get global style defaults ---
    style_config = config.get('style', {})
    default_color_cycle = itertools.cycle(style_config.get('color', {}).get('cycle', ['#1f77b4', '#ff7f0e', '#2ca02c']))

    # --- Pre-calculate global bins for consistent marginal histograms ---
    marginal_config = config.get('marginal', {})
    xaxis_config = config.get('xaxis', {})
    yaxis_config = config.get('yaxis', {})

    global_log_bins_x = None
    if has_marginal_x and xaxis_config.get('scale') == 'log' and marginal_config.get('kind') == 'hist':
        all_x_vals = [d['x'] for d in data if d['x'] > 0]
        if all_x_vals:
            num_bins = marginal_config.get('bins', 25)
            min_val = np.log10(min(all_x_vals))
            max_val = np.log10(max(all_x_vals))
            global_log_bins_x = np.logspace(min_val, max_val, num_bins)

    global_log_bins_y = None
    if has_marginal_y and yaxis_config.get('scale') == 'log' and marginal_config.get('kind') == 'hist':
        all_y_vals = [d['y'] for d in data if d['y'] > 0]
        if all_y_vals:
            num_bins = marginal_config.get('bins', 25)
            min_val = np.log10(min(all_y_vals))
            max_val = np.log10(max(all_y_vals))
            global_log_bins_y = np.logspace(min_val, max_val, num_bins)


    # --- Loop through data series to draw traces ---
    for series_id, series_data in data_by_series.items():
        # This loop is the "Data-Driven Loop" mentioned in the spec (Section 5.2, item 6)

        # --- Section 5.1.2: Style Precedence Logic ---
        # Get series-specific config, or an empty dict if not defined
        series_config = next((s for s in config.get('series', []) if s.get('id') == series_id), {})

        # Determine colors and styles based on precedence
        marker_color = series_config.get('marker', {}).get('facecolor', next(default_color_cycle))
        line_color = series_config.get('line', {}).get('color', marker_color)

        # Extract data for this series
        x_vals = [d['x'] for d in series_data]
        y_vals = [d['y'] for d in series_data]
        z_vals = [d['z'] for d in series_data]
        error_x_vals = [d['error_x'] for d in series_data]
        error_y_vals = [d['error_y'] for d in series_data]

        # --- Section 2.2: Draw Main Scatter/Line Trace ---
        main_trace_args = {
            'x': x_vals,
            'y': y_vals,
            'mode': 'markers',
            'name': series_id,
            'legendgroup': series_id,
            'marker': {
                'symbol': series_config.get('marker', {}).get('symbol', style_config.get('marker', {}).get('default_symbol', 'circle')),
                'size': series_config.get('marker', {}).get('size', style_config.get('marker', {}).get('default_size', 8)),
                'color': marker_color,
                'opacity': series_config.get('alpha', style_config.get('marker', {}).get('default_alpha', 0.7)),
                'line': {
                    'color': series_config.get('marker', {}).get('linecolor', 'white'),
                    'width': series_config.get('marker', {}).get('edgewidth', style_config.get('marker', {}).get('default_edgewidth', 1))
                }
            },
            'line': {
                'color': line_color,
                'width': style_config.get('linewidth', {}).get('main', 2),
                'dash': series_config.get('line', {}).get('style', 'solid')
            }
        }

        # Add error bars if configured
        if config.get('error_bars', {}).get('visible', False):
            main_trace_args['error_x'] = {'type': 'data', 'array': error_x_vals, 'visible': True}
            main_trace_args['error_y'] = {'type': 'data', 'array': error_y_vals, 'visible': True}

        # Handle Z-value colormap (Highest Priority)
        if config.get('colorbar', {}).get('visible', False):
            main_trace_args['marker']['color'] = z_vals
            main_trace_args['marker']['colorscale'] = config.get('colorbar', {}).get('cmap', 'Viridis')
            main_trace_args['marker']['showscale'] = True
            main_trace_args['marker']['colorbar'] = {'title': config.get('colorbar', {}).get('title', 'Z Value')}
            # Only show one colorbar for the first series
            if len(fig.data) > 0:
                 main_trace_args['marker']['showscale'] = False


        main_row, main_col = (2, 1) if (has_marginal_x and has_marginal_y) else \
                             (2, 1) if has_marginal_x else \
                             (1, 1) if has_marginal_y else \
                             (1, 1)
        fig.add_trace(go.Scatter(**main_trace_args), row=main_row, col=main_col)

        # --- Section 2.4: Draw Marginal Traces ---
        marginal_kind = marginal_config.get('kind', 'hist')

        if has_marginal_x:
            if marginal_kind == 'hist':
                # --- IMPLEMENTED: Manual Logarithmic Binning (Rule R-1 & R-2) ---
                if global_log_bins_x is not None:
                    positive_x = [v for v in x_vals if v > 0]
                    # Use density=True to ensure area represents probability
                    counts, bins = np.histogram(positive_x, bins=global_log_bins_x, density=True)
                    bin_centers = (bins[:-1] + bins[1:]) / 2
                    bin_widths = np.diff(bins)
                    fig.add_trace(go.Bar(
                        x=bin_centers, y=counts, width=bin_widths,
                        name=series_id, legendgroup=series_id, showlegend=False,
                        marker_color=marker_color
                    ), row=1, col=main_col)
                else: # Fallback to linear histogram
                    fig.add_trace(go.Histogram(x=x_vals, name=series_id, legendgroup=series_id, showlegend=False, marker_color=marker_color), row=1, col=main_col)
            elif marginal_kind == 'box':
                fig.add_trace(go.Box(x=x_vals, name=series_id, legendgroup=series_id, showlegend=False, marker_color=marker_color), row=1, col=main_col)

        if has_marginal_y:
            if marginal_kind == 'hist':
                if global_log_bins_y is not None:
                    positive_y = [v for v in y_vals if v > 0]
                    counts, bins = np.histogram(positive_y, bins=global_log_bins_y, density=True)
                    bin_centers = (bins[:-1] + bins[1:]) / 2
                    bin_widths = np.diff(bins)
                    fig.add_trace(go.Bar(
                        y=bin_centers, x=counts, width=bin_widths, orientation='h',
                        name=series_id, legendgroup=series_id, showlegend=False,
                        marker_color=marker_color
                    ), row=main_row, col=2)
                else: # Fallback to linear histogram
                    fig.add_trace(go.Histogram(y=y_vals, name=series_id, legendgroup=series_id, showlegend=False, marker_color=marker_color), row=main_row, col=2)
            elif marginal_kind == 'box':
                fig.add_trace(go.Box(y=y_vals, name=series_id, legendgroup=series_id, showlegend=False, marker_color=marker_color), row=main_row, col=2)


    # --- Apply Layout and Global Configurations ---
    # This is the "Configuration Post-processing" step (Section 5.2, item 6)

    # Section 2.1 & 2.5: Axes, Title, and Legend
    layout_updates = {
        'title_text': config.get('figure', {}).get('title', 'Cross-Plot Prototype'),
        'plot_bgcolor': style_config.get('color', {}).get('background_plot', 'white'),
        'paper_bgcolor': style_config.get('color', {}).get('background_canvas', 'white'),
        'font': {'family': style_config.get('font', {}).get('family', 'Arial')},
        # --- FIX: Set a fixed position for the legend to avoid overlap ---
        'legend': {
            'x': 1.02,
            'y': 1,
            'xanchor': 'left',
            'yanchor': 'top',
            'traceorder': 'normal',
            'bgcolor': 'rgba(255,255,255,0.5)'
        }
    }

    # --- CORRECTED & SIMPLIFIED AXIS CONFIGURATION LOGIC ---
    # This block replaces the previous complex and buggy logic.

    # Determine main plot axis names based on layout
    if has_marginal_x and has_marginal_y:
        # 2x2 grid: main plot at (2,1) -> axes are xaxis3, yaxis3
        main_xaxis_name = 'xaxis3'
        main_yaxis_name = 'yaxis3'
    elif has_marginal_x:
        # 2x1 grid: main plot at (2,1) -> axes are xaxis2, yaxis2
        main_xaxis_name = 'xaxis2'
        main_yaxis_name = 'yaxis2'
    else: # This covers 1x2 (marginal y) and 1x1 (no marginals)
        # Main plot is at (1,1) -> axes are xaxis, yaxis
        main_xaxis_name = 'xaxis'
        main_yaxis_name = 'yaxis'

    # --- Read grid configuration from the config object ---
    grid_x_config = xaxis_config.get('grid', {}).get('major', {})
    grid_y_config = yaxis_config.get('grid', {}).get('major', {})

    # Apply titles and scales to the main plot axes
    layout_updates[main_xaxis_name] = {
        'title': xaxis_config.get('label', 'X-Axis'),
        'type': xaxis_config.get('scale', 'linear'),
        'showgrid': grid_x_config.get('on', False),
        'gridcolor': grid_x_config.get('color', style_config.get('color', {}).get('grid', '#e5e5e5')),
        'gridwidth': grid_x_config.get('linewidth', style_config.get('linewidth', {}).get('grid_major', 0.6))
    }
    layout_updates[main_yaxis_name] = {
        'title': yaxis_config.get('label', 'Y-Axis'),
        'type': yaxis_config.get('scale', 'linear'),
        'showgrid': grid_y_config.get('on', False),
        'gridcolor': grid_y_config.get('color', style_config.get('color', {}).get('grid', '#e5e5e5')),
        'gridwidth': grid_y_config.get('linewidth', style_config.get('linewidth', {}).get('grid_major', 0.6))
    }

    # --- Apply border and tick styles globally to all axes ---
    fig.update_xaxes(
        showline=True,
        linewidth=style_config.get('frame', {}).get('width', 1),
        linecolor=style_config.get('frame', {}).get('color', '#666666'),
        ticks='outside'
    )
    fig.update_yaxes(
        showline=True,
        linewidth=style_config.get('frame', {}).get('width', 1),
        linecolor=style_config.get('frame', {}).get('color', '#666666'),
        ticks='outside'
    )

    fig.update_layout(**layout_updates)

    # --- FIX: EXPLICITLY SET MARGINAL AXIS TYPES TO MATCH MAIN AXES ---
    # This robustly ensures shared axes properties (like 'type') are correctly
    # propagated, which is crucial for perfect visual alignment.
    if has_marginal_x and xaxis_config.get('scale') == 'log':
        fig.update_xaxes(type='log', row=1, col=1)

    if has_marginal_y and yaxis_config.get('scale') == 'log':
        # Determine the correct marginal y-axis to update
        if has_marginal_x: # 2x2 layout
            fig.update_yaxes(type='log', row=2, col=2)
        else: # 1x2 layout
            fig.update_yaxes(type='log', row=1, col=2)

    # Barmode for histograms
    if marginal_kind == 'hist':
        fig.update_layout(barmode='stack')

    return fig


# ==============================================================================
# Section: Example Generation
# As per the prompt, we generate and save three different examples.
# ==============================================================================
if __name__ == "__main__":
    print("Generating cross-plot prototypes...")

    # --- Create output directory if it doesn't exist ---
    output_dir = "crossplot_prototype_output_plotly6"
    os.makedirs(output_dir, exist_ok=True)
    print(f"Output directory '{output_dir}' is ready.")

    # --- Generate common data for all examples ---
    sample_data = generate_sample_data()

    # --- Example 1: Basic Linear Plot with Error Bars ---
    config1 = {
        'figure': {'title': 'Example 1: Basic Linear Plot with Error Bars'},
        'xaxis': {
            'label': 'GR (API)',
            'scale': 'linear',
            'grid': {'major': {'on': True}}
        },
        'yaxis': {
            'label': 'Resistivity (ohm.m)',
            'scale': 'linear',
            'grid': {'major': {'on': True}}
        },
        'error_bars': {'visible': True},
        'series': [
            {'id': '岩性A', 'marker': {'symbol': 'circle'}},
            {'id': '岩性B', 'marker': {'symbol': 'square'}}
        ],
        'style': {
            'color': {'cycle': ['#2ca02c', '#d62728']}
        }
    }
    fig1 = create_crossplot(config1, sample_data)
    file_path1 = os.path.join(output_dir, "prototype_linear.png")
    fig1.write_image(file_path1, width=800, height=600, scale=2)
    print(f"Saved: {file_path1}")

    # --- Example 2: Log-Linear Plot with Marginal Histograms ---
    config2 = {
        'figure': {'title': 'Example 2: Log-Linear Plot with Marginal Histograms'},
        'xaxis': {
            'label': 'Permeability (mD)',
            'scale': 'log',
            'grid': {'major': {'on': True}}
        },
        'yaxis': {
            'label': 'Porosity (%)',
            'scale': 'linear',
            'grid': {'major': {'on': True}}
        },
        'marginal': {
            'enabled': 'both',
            'kind': 'hist',
            'bins': 25
        },
        'series': [
            {'id': '岩性A'},
            {'id': '岩性B'}
        ],
        'style': {
             'color': {'cycle': ['#1f77b4', '#ff7f0e']}
        }
    }
    fig2 = create_crossplot(config2, sample_data)
    file_path2 = os.path.join(output_dir, "prototype_log_hist.png")
    fig2.write_image(file_path2, width=800, height=800, scale=2)
    print(f"Saved: {file_path2}")

    # --- Example 3: Complex Plot with Colormap and Marginal Box Plots ---
    config3 = {
        'figure': {'title': 'Example 3: Colormap Plot with Colormap and Marginal Box Plots'},
        'xaxis': {
            'label': 'Acoustic Impedance (m/s * g/cc)',
            'grid': {'major': {'on': True}}
        },
        'yaxis': {
            'label': 'Vp/Vs Ratio',
            'grid': {'major': {'on': True}}
        },
        'colorbar': {
            'visible': True,
            'title': 'Depth (m)',
            'cmap': 'Plasma'
        },
        'marginal': {
            'enabled': 'both',
            'kind': 'box'
        },
        'series': [
            {'id': '岩性A'},
            {'id': '岩性B'}
        ]
    }
    fig3 = create_crossplot(config3, sample_data)
    file_path3 = os.path.join(output_dir, "prototype_complex_box.png")
    fig3.write_image(file_path3, width=800, height=800, scale=2)
    print(f"Saved: {file_path3}")

    print("All prototypes generated successfully.")

