

## Gemini Code Assist 缓存信息的目录列表

https://developers.google.com/gemini-code-assist/docs/set-up-gemini-standard-enterprise?hl=zh-cn#cache-directories

``` text

%LOCALAPPDATA%/cloud-code

%LOCALAPPDATA%/google-vscode-extension

```
## 编写代码

https://developers.google.com/gemini-code-assist/docs/write-code-gemini?hl=zh-cn

### 根据提示生成代码

- 在代码文件中，另起一行，按 Ctrl+I（适用于 Windows 和 Linux）或 Command+I（适用于 macOS），以打开 Gemini Code Assist 快速选择菜单。

- 在菜单中，使用 /generate 命令输入 /generate function to create a Cloud Storage bucket，然后按 Enter 键（Windows 和 Linux）或 Return 键 (macOS)。


### 通过注释在代码文件中提示 Gemini Code Assist

- 在新的代码行中，输入注释 Function to create a Cloud Storage bucket，然后按 Enter 键（Windows 和 Linux）或 Return 键 (macOS)。

- 如需生成代码，请按 Ctrl+Enter（Windows 和 Linux）或 Ctrl+Return (macOS)。

- Gemini Code Assist 会在提示文本旁以灰显形式生成代码。

- 可选：若要接受生成的代码，请按 Tab 键。

