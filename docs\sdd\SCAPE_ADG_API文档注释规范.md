# SCAPE_ADG_API文档注释规范

> **版本**: 4.0  **日期**: 2025-07-01
> **基于**: SCAPE_SAD_软件架构设计.md、SCAPE_MS_方法说明书.md、SCAPE_CCG_编码与通用规范.md
> **备份**: 原3.0版本已备份为 `SCAPE_ADG_API文档注释规范_v3_backup.md`

## 1 概述

### 1.1 文档定位

本规范定义SCAPE项目API文档和代码注释的编写标准，确保：
- **科学严谨性**：算法实现与《SCAPE_MS_方法说明书》保持一致
- **架构一致性**：遵循五层分层架构的设计原则
- **类型安全性**：充分利用Python 3.11+的现代化类型系统
- **工程质量**：支持IDE智能提示、自动化文档生成和代码质量检查

### 1.2 适用范围

**强制适用**：
- 所有公共API（`__init__.py`中导出的函数、类、常量）
- 核心算法实现（OBMIQ、FOSTER-NMR、SWIFT-PSO）
- 数据模型类（WpWellProject、WpDataset系列）
- 异常类和常量枚举

**推荐适用**：
- 内部工具函数和辅助类
- 测试用例和示例代码
- 配置文件和脚本

### 1.3 设计原则

**ADG-P1 科学研究优先**：API文档必须准确反映算法的科学原理和数学模型
**ADG-P2 分层架构对应**：文档结构与五层架构保持一致，明确依赖关系
**ADG-P3 类型安全驱动**：充分利用Protocol、TypedDict等现代类型特性
**ADG-P4 工具链集成**：支持ruff、mypy、sphinx等现代化工具链
**ADG-P5 实用主义**：避免过度文档化，重点关注核心功能和使用场景

## 2 API文档编写规范

### 2.1 文档字符串格式

**ADG-R1** 使用Google风格的docstring格式，支持sphinx自动生成文档：

```python
def read_wp_excel(
    file_path: str | Path,
    *,
    chunk_size: int = 10000,
    validate_schema: bool = True,
    gpu_accelerated: bool = False
) -> WpWellProject:
    """读取WP格式Excel文件并构建井工程项目对象。

    本函数实现SCAPE项目标准的WP文件格式解析，支持流式读取大文件
    和GPU加速数据处理。文件格式规范参见《SCAPE_MS_方法说明书》第1.1节。

    Args:
        file_path: WP格式Excel文件路径，支持相对路径和绝对路径
        chunk_size: 流式读取的块大小，默认10000行。大文件建议增大此值
        validate_schema: 是否执行严格的数据模式验证，默认True
        gpu_accelerated: 是否启用GPU加速数据处理，需要CUDA环境

    Returns:
        WpWellProject: 完整的井工程项目对象，包含所有数据集和元数据

    Raises:
        WpFileFormatError: 文件格式不符合WP标准
        WpSchemaValidationError: 数据模式验证失败
        WpIOError: 文件读取IO错误

    Example:
        >>> # 基本用法
        >>> project = read_wp_excel("data/well_001.wp.xlsx")
        >>> print(f"井名: {project.well_name}")
        >>>
        >>> # 大文件优化
        >>> project = read_wp_excel(
        ...     "data/large_well.wp.xlsx",
        ...     chunk_size=50000,
        ...     gpu_accelerated=True
        ... )

    Note:
        - 文件必须包含标准的五行表头格式
        - 支持head_info和wn_map特殊工作表
        - GPU加速需要安装cupy和cudf依赖

    See Also:
        - WpWellProject: 井工程项目核心数据模型
        - write_wp_excel: 对应的写入函数
        - WpExcelReader: 底层流式读取器

    References:
        《SCAPE_MS_方法说明书》第1.1节 - WP文件格式规范
    """
```

### 2.2 类型注解规范

**ADG-R2** 所有公共API必须提供完整的类型注解：

```python
from typing import Protocol, TypedDict, Literal, overload
from logwp.typing_ext import WpDataFrame, JsonDict

class DatasetProtocol(Protocol):
    """数据集协议接口，定义所有数据集类型的共同行为。"""

    @property
    def ds_type(self) -> WpDsType: ...

    @property
    def df(self) -> WpDataFrame: ...

    def validate(self) -> bool: ...

class WpValidationResult(TypedDict):
    """数据验证结果的类型化字典。"""
    is_valid: bool
    error_count: int
    warnings: list[str]
    details: JsonDict

# 使用Literal类型限制参数选项
def resample_curve(
    df: WpDataFrame,
    method: Literal["linear", "cubic", "fir"] = "linear",
    target_rate: float = 0.1
) -> WpDataFrame:
    """重采样测井曲线数据。"""
```

### 2.3 算法文档特殊要求

**ADG-R3** 核心算法必须包含数学公式和参数说明：

```python
def foster_nmr_permeability(
    phit: np.ndarray,
    t2lm: np.ndarray,
    *,
    c_factor: float = 4.0,
    alpha: float = 2.0,
    correction_factor: float = 1.0
) -> np.ndarray:
    """FOSTER-NMR渗透率计算公式实现。

    基于核磁共振测井数据计算碳酸盐岩渗透率，实现经典FOSTER-NMR方程：

    K = C × (φ^α) × (T2LM^β) × CF

    其中：
    - K: 渗透率 (mD)
    - φ: 总孔隙度 (v/v)
    - T2LM: T2对数平均值 (ms)
    - C: 岩石系数，默认4.0
    - α: 孔隙度指数，默认2.0
    - β: T2指数，固定为2.0
    - CF: 油基泥浆校正因子

    Args:
        phit: 总孔隙度数组，范围[0, 1]
        t2lm: T2对数平均值数组，单位ms
        c_factor: 岩石系数C，影响绝对渗透率水平
        alpha: 孔隙度指数α，控制孔隙度敏感性
        correction_factor: 油基泥浆校正因子，来自OBMIQ模型预测

    Returns:
        np.ndarray: 渗透率数组，单位mD

    References:
        《SCAPE_MS_方法说明书》第2.2节 - FOSTER-NMR方程
        Foster et al. (1982) - 原始FOSTER-NMR公式
    """
```

### 2.4 异常文档规范

**ADG-R4** 异常类必须详细说明触发条件和处理建议：

```python
class WpSchemaValidationError(WpError):
    """数据模式验证失败异常。

    当WP文件数据不符合预定义的数据模式时抛出此异常。
    常见触发场景包括：
    - 必需列缺失
    - 数据类型不匹配
    - 数值范围超出限制
    - 深度索引不连续

    Attributes:
        dataset_type: 验证失败的数据集类型
        failed_columns: 验证失败的列名列表
        error_details: 详细错误信息字典

    Example:
        >>> try:
        ...     project = read_wp_excel("invalid_file.xlsx")
        ... except WpSchemaValidationError as e:
        ...     print(f"验证失败: {e.dataset_type}")
        ...     print(f"问题列: {e.failed_columns}")
        ...     # 根据错误详情进行数据修复
        ...     fix_data_issues(e.error_details)
    """

    def __init__(
        self,
        message: str,
        *,
        dataset_type: WpDsType,
        failed_columns: list[str],
        error_details: JsonDict
    ) -> None:
        super().__init__(message)
        self.dataset_type = dataset_type
        self.failed_columns = failed_columns
        self.error_details = error_details
```

## 3 代码注释编写规范

### 3.1 模块级注释

**ADG-R5** 每个模块文件必须包含模块级文档字符串：

```python
"""logwp.models.well_project - 井工程项目核心数据模型。

本模块实现SCAPE项目的核心数据模型WpWellProject，作为DDD架构中的
聚合根(Aggregate Root)，负责管理井工程项目的完整数据生命周期。

主要功能：
- 井工程数据的统一管理和访问
- 数据集的增删改查操作
- 数据一致性验证和约束检查
- 直接数据集访问和管理

架构位置：
- 层级：logwp核心数据层
- 依赖：constants, typing_ext, datasets
- 被依赖：io层、上层业务逻辑

Classes:
    WpWellProject: 井工程项目聚合根

Functions:
    create_empty_project: 创建空的井工程项目

Constants:
    DEFAULT_PROJECT_NAME: 默认项目名称

Example:
    >>> from logwp.models import WpWellProject
    >>> project = WpWellProject("WELL-001")
    >>> project.add_dataset("logs", continuous_data)

See Also:
    - logwp.datasets: 数据集实现
    - logwp.io: 文件读写操作

References:
    《SCAPE_SAD_软件架构设计》第5.2节 - 核心对象模型设计
"""

from __future__ import annotations
import logging
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.datasets import DatasetProtocol

logger = logging.getLogger(__name__)
```

### 3.2 复杂算法注释

**ADG-R6** 复杂算法实现必须包含详细的行内注释：

```python
def swift_pso_optimization(
    objective_func: Callable[[np.ndarray], float],
    bounds: list[tuple[float, float]],
    *,
    n_particles: int = 30,
    max_iterations: int = 100,
    w: float = 0.9,
    c1: float = 2.0,
    c2: float = 2.0
) -> OptimizationResult:
    """SWIFT-PSO参数优化算法实现。"""

    # 初始化粒子群 - 使用拉丁超立方采样确保参数空间均匀覆盖
    particles = _initialize_particles_lhs(bounds, n_particles)
    velocities = np.zeros_like(particles)

    # 个体最优和全局最优初始化
    personal_best = particles.copy()
    personal_best_scores = np.array([objective_func(p) for p in particles])
    global_best_idx = np.argmin(personal_best_scores)
    global_best = personal_best[global_best_idx].copy()

    # 自适应惯性权重策略 - 随迭代次数线性递减
    w_start, w_end = 0.9, 0.4

    for iteration in range(max_iterations):
        # 动态调整惯性权重：前期探索，后期开发
        current_w = w_start - (w_start - w_end) * iteration / max_iterations

        for i in range(n_particles):
            # 生成随机因子 - 确保每个维度独立随机
            r1, r2 = np.random.random(2)

            # PSO速度更新公式：v = w*v + c1*r1*(pbest-x) + c2*r2*(gbest-x)
            velocities[i] = (
                current_w * velocities[i] +                    # 惯性项
                c1 * r1 * (personal_best[i] - particles[i]) +  # 认知项
                c2 * r2 * (global_best - particles[i])         # 社会项
            )

            # 位置更新并应用边界约束
            particles[i] += velocities[i]
            particles[i] = np.clip(particles[i],
                                 [b[0] for b in bounds],
                                 [b[1] for b in bounds])

            # 评估新位置并更新个体最优
            score = objective_func(particles[i])
            if score < personal_best_scores[i]:
                personal_best_scores[i] = score
                personal_best[i] = particles[i].copy()

                # 检查是否需要更新全局最优
                if score < personal_best_scores[global_best_idx]:
                    global_best_idx = i
                    global_best = particles[i].copy()

        # 收敛性检查 - 基于全局最优值的改善程度
        if iteration > 10:  # 允许前10次迭代的初始波动
            recent_improvements = np.diff(
                [personal_best_scores[global_best_idx]
                 for _ in range(max(0, iteration-5), iteration+1)]
            )
            if np.all(np.abs(recent_improvements) < 1e-6):
                logger.debug(f"PSO在第{iteration}次迭代收敛")
                break

    return OptimizationResult(
        best_params=global_best,
        best_score=personal_best_scores[global_best_idx],
        iterations=iteration + 1,
        convergence_history=convergence_history
    )
```

### 3.3 性能关键代码注释

**ADG-R7** 性能关键代码必须包含性能考虑和优化说明：

```python
def process_large_dataset(
    df: WpDataFrame,
    *,
    chunk_size: int = 10000,
    use_gpu: bool = False,
    memory_limit_gb: float = 2.0
) -> WpDataFrame:
    """处理大型数据集，支持内存优化和GPU加速。"""

    # 内存预检查 - 避免OOM错误
    estimated_memory_gb = df.memory_usage(deep=True).sum() / 1e9
    if estimated_memory_gb > memory_limit_gb:
        logger.warning(f"数据集大小{estimated_memory_gb:.1f}GB超过限制{memory_limit_gb}GB")
        # 启用分块处理模式
        return _process_in_chunks(df, chunk_size, use_gpu)

    if use_gpu and _cuda_available():
        # GPU加速路径 - 适用于数值计算密集型操作
        import cudf
        gpu_df = cudf.from_pandas(df)
        result = _gpu_process_dataframe(gpu_df)
        return result.to_pandas()  # 转回CPU内存
    else:
        # CPU优化路径 - 使用向量化操作避免Python循环
        return _cpu_vectorized_process(df)
```

### 3.4 业务逻辑注释

**ADG-R8** 复杂业务逻辑必须解释业务规则和决策依据：

```python
def validate_well_project_consistency(project: WpWellProject) -> ValidationResult:
    """验证井工程项目数据的一致性。"""

    errors = []
    warnings = []

    # 业务规则1：所有数据集必须共享相同的井名
    # 依据：《SCAPE_MS_方法说明书》第1.2节 - 数据完整性要求
    well_names = {ds.well_name for ds in project.datasets.values()}
    if len(well_names) > 1:
        errors.append(f"检测到多个井名: {well_names}")

    # 业务规则2：连续数据集的深度范围必须覆盖点数据
    # 依据：确保点数据能够正确映射到连续曲线
    continuous_datasets = [ds for ds in project.datasets.values()
                          if ds.ds_type == WpDsType.CONTINUOUS]
    point_datasets = [ds for ds in project.datasets.values()
                     if ds.ds_type == WpDsType.POINT]

    if continuous_datasets and point_datasets:
        cont_depth_range = _get_depth_range(continuous_datasets)
        for point_ds in point_datasets:
            point_depths = point_ds.df[WpKeywords.DEPTH.value]
            out_of_range = point_depths[
                (point_depths < cont_depth_range[0]) |
                (point_depths > cont_depth_range[1])
            ]
            if len(out_of_range) > 0:
                warnings.append(
                    f"点数据集{point_ds.name}中{len(out_of_range)}个点超出连续数据深度范围"
                )

    # 业务规则3：NMR相关数据集必须包含必需的物理量
    # 依据：FOSTER-NMR算法要求
    nmr_datasets = [ds for ds in project.datasets.values()
                   if "nmr" in ds.name.lower()]
    for nmr_ds in nmr_datasets:
        required_columns = [WpKeywords.PHIT.value, WpKeywords.T2LM.value]
        missing_columns = [col for col in required_columns
                          if col not in nmr_ds.df.columns]
        if missing_columns:
            errors.append(
                f"NMR数据集{nmr_ds.name}缺少必需列: {missing_columns}"
            )

    return ValidationResult(
        is_valid=len(errors) == 0,
        errors=errors,
        warnings=warnings
    )
```

## 4 AI编程助手API使用规范

### 4.1 杜绝臆测原则

**ADG-R9** AI编程助手必须严格遵循"杜绝臆测"原则，所有代码生成必须基于实际代码库状态：

#### 4.1.1 禁止臆测的内容

**绝对禁止臆测**：
- API函数的参数名称、类型和默认值
- 类的方法名称和方法签名
- 常量和枚举的具体成员名称
- 异常类的层次结构和属性
- 模块的导入路径和可用性
- 配置文件的格式和字段名称

**必须通过工具确认**：
- 使用`codebase-retrieval`工具查询API定义
- 使用`view`工具查看具体实现代码
- 使用`diagnostics`工具检查代码问题
- 通过实际测试验证API行为

#### 4.1.2 臆测检查清单

在生成任何代码前，AI编程助手必须确认：

- [ ] **ADG-C29** 已通过工具确认所有使用的函数名称和签名
- [ ] **ADG-C30** 已验证所有导入的模块和类确实存在
- [ ] **ADG-C31** 已确认所有常量和枚举成员的准确名称
- [ ] **ADG-C32** 已检查异常类的实际定义和构造参数
- [ ] **ADG-C33** 已验证配置文件和数据格式的实际结构
- [ ] **ADG-C34** 已确认架构层次和依赖关系的准确性

#### 4.1.3 错误臆测示例

```python
# ❌ 错误：臆测API参数名称
project = read_wp_excel(
    filepath="data.xlsx",        # 臆测参数名为filepath
    validate=True,               # 臆测参数名为validate
    chunk=5000                   # 臆测参数名为chunk
)

# ✅ 正确：通过工具确认实际API
# 先执行：codebase-retrieval("read_wp_excel函数的完整参数签名")
# 再执行：view("logwp/io/__init__.py", search_query_regex="def read_wp_excel")
project = read_wp_excel(
    file_path="data.xlsx",       # 确认参数名为file_path
    validate_schema=True,        # 确认参数名为validate_schema
    chunk_size=5000              # 确认参数名为chunk_size
)

# ❌ 错误：臆测常量名称
if dataset.ds_type == WpDsType.CONTINUOUS_DATA:  # 臆测枚举成员名

# ✅ 正确：确认实际常量定义
# 先执行：codebase-retrieval("WpDsType枚举的所有成员定义")
if dataset.ds_type == WpDsType.CONTINUOUS:       # 确认实际成员名

# ❌ 错误：臆测异常构造参数
raise WpValidationError(
    "验证失败",
    error_type="schema",         # 臆测异常有error_type参数
    details={"column": "PHIT"}   # 臆测异常有details参数
)

# ✅ 正确：确认异常类实际定义
# 先执行：view("logwp/exceptions.py", search_query_regex="class WpValidationError")
raise WpValidationError(
    "验证失败",
    dataset_type=WpDsType.CONTINUOUS,  # 确认实际参数名
    failed_columns=["PHIT"],           # 确认实际参数名
    error_details={"reason": "missing"}  # 确认实际参数名
)
```

### 4.2 API探查流程

**ADG-R10** AI编程助手在生成代码前必须执行完整的API探查流程：

#### 步骤1：架构层次定位
```bash
# 使用codebase-retrieval工具确定API在五层架构中的位置
codebase-retrieval("WpWellProject类的完整定义，包括其在logwp层的架构位置和依赖关系")
codebase-retrieval("OBMIQ算法的实现位置和接口定义，包括其在scape/core层的作用")
```

#### 步骤2：类型系统确认
```bash
# 确认现代化类型注解的使用
codebase-retrieval("logwp.typing_ext模块的所有类型别名和Protocol定义")
codebase-retrieval("WpDataFrame、JsonDict等类型别名的具体定义")
```

#### 步骤3：常量和枚举验证
```bash
# 确认常量的正确使用
codebase-retrieval("logwp.constants模块的所有枚举类和常量定义")
codebase-retrieval("WpKeywords、WpDsType等枚举的完整成员列表")
```

#### 步骤4：异常体系了解
```bash
# 了解项目的异常处理策略
codebase-retrieval("logwp.exceptions模块的异常类层次结构")
codebase-retrieval("WpError及其子类的定义和使用场景")
```

#### 步骤5：实际代码查看
```bash
# 使用view工具查看具体实现
view("logwp/models/well_project.py", type="file", search_query_regex="class WpWellProject")
view("scape/core/obmiq.py", type="file", search_query_regex="class OBMIQModel")
```

### 4.3 代码生成规范

**ADG-R11** AI编程助手生成的代码必须遵循以下规范：

```python
# 正确示例：遵循SCAPE项目规范
from __future__ import annotations

import logging
from typing import TYPE_CHECKING

import numpy as np
import pandas as pd

from logwp.constants import WpKeywords as K, WpDsType
from logwp.exceptions import WpValidationError
from logwp.typing_ext import WpDataFrame, JsonDict

if TYPE_CHECKING:
    from logwp.models import WpWellProject

logger = logging.getLogger(__name__)

def process_nmr_data(
    project: WpWellProject,
    *,
    t2_cutoffs: tuple[float, float] = (3.0, 33.0),
    validate_input: bool = True
) -> JsonDict:
    """处理NMR数据并计算孔隙度组分。

    基于T2谱数据计算微孔、中孔、大孔组分，实现三组分孔隙度分割。
    算法依据《SCAPE_MS_方法说明书》第2.1节。

    Args:
        project: 包含NMR数据的井工程项目
        t2_cutoffs: T2截止值元组(微孔-中孔, 中孔-大孔)，单位ms
        validate_input: 是否验证输入数据完整性

    Returns:
        JsonDict: 包含孔隙度组分计算结果的字典

    Raises:
        WpValidationError: 输入数据验证失败

    References:
        《SCAPE_MS_方法说明书》第2.1节 - NMR孔隙度分割
    """
    if validate_input:
        _validate_nmr_input(project)

    # 获取NMR数据集 - 使用常量避免硬编码
    nmr_datasets = [
        ds for ds in project.datasets.values()
        if ds.ds_type == WpDsType.CONTINUOUS and "nmr" in ds.name.lower()
    ]

    if not nmr_datasets:
        raise WpValidationError("项目中未找到NMR连续数据集")

    nmr_df = nmr_datasets[0].df

    # 使用枚举常量访问列名
    phit_col = K.PHIT.value
    t2lm_col = K.T2LM.value

    if phit_col not in nmr_df.columns or t2lm_col not in nmr_df.columns:
        missing_cols = [col for col in [phit_col, t2lm_col]
                       if col not in nmr_df.columns]
        raise WpValidationError(f"NMR数据集缺少必需列: {missing_cols}")

    # 计算孔隙度组分
    result = {
        "total_porosity": nmr_df[phit_col].mean(),
        "t2_cutoffs": t2_cutoffs,
        "processing_timestamp": pd.Timestamp.now().isoformat()
    }

    logger.info(f"NMR数据处理完成，总孔隙度: {result['total_porosity']:.3f}")

    return result

def _validate_nmr_input(project: WpWellProject) -> None:
    """验证NMR输入数据的完整性。"""
    # 实现验证逻辑...
    pass
```

### 4.4 错误代码示例

**ADG-R12** 以下是AI编程助手应该避免的错误代码模式：

```python
# 错误示例1：硬编码字符串和魔法数字
def bad_example_1(df):
    # ❌ 硬编码列名
    if "PHIT" not in df.columns:
        return None

    # ❌ 魔法数字
    return df["PHIT"] * 0.85

# 错误示例2：缺少类型注解
def bad_example_2(data, params):  # ❌ 无类型注解
    # ❌ 无文档字符串
    result = []
    for item in data:  # ❌ 低效的Python循环
        result.append(item * params)
    return result

# 错误示例3：违反架构依赖关系
from scape.core import OBMIQModel  # ❌ logwp层不应依赖scape层

def bad_example_3():
    # ❌ 违反分层架构原则
    model = OBMIQModel()
    return model.predict(data)

# 错误示例4：不当的异常处理
def bad_example_4():
    try:
        # 一些操作
        pass
    except Exception as e:  # ❌ 捕获过于宽泛的异常
        print(f"Error: {e}")  # ❌ 使用print而非logging
        return None  # ❌ 静默失败
```

## 5 分层架构文档规范

### 5.1 架构层次对应

**ADG-R13** API文档必须明确标识所属的架构层次：

```python
"""scape.core.obmiq - OBMIQ机器学习模型实现。

架构层次：scape/core (第3层 - SCAPE专属算法层)
依赖关系：
- 向下依赖：logwp (数据模型), logwp_extras (特征工程)
- 向上被依赖：scape/study (实验复现), scape_case (应用案例)
- 横切依赖：无

本模块实现SCAPE Framework Stage-1的OBMIQ算法，用于预测油基泥浆
侵入校正因子。算法详细说明参见《SCAPE_MS_方法说明书》第1节。
"""
```

### 5.2 依赖关系文档

**ADG-R14** 跨层API调用必须在文档中明确说明依赖关系：

```python
def create_scape_pipeline(
    project: WpWellProject,  # 来自logwp层
    config: ScapeConfig      # 来自scape/core层
) -> ScapePipeline:
    """创建SCAPE完整处理流水线。

    本函数位于scape/study层，整合logwp数据模型和scape/core算法，
    构建端到端的渗透率预测流水线。

    依赖关系：
    - logwp.models.WpWellProject: 井工程数据模型 (向下依赖)
    - scape.core.ScapePipeline: 算法流水线 (向下依赖)
    - logwp_extras.features: 特征工程 (向下依赖)

    架构约束：
    - 不得直接访问logwp.io层的内部实现
    - 不得被logwp或scape/core层调用 (单向依赖)
    """
```

## 6 现代化技术栈规范

### 6.1 类型安全要求

**ADG-R15** 充分利用Python 3.11+的现代类型特性：

```python
from typing import Protocol, TypedDict, Literal, TypeVar, Generic
from typing_extensions import NotRequired, Required

# 使用Protocol定义接口
class ProcessorProtocol(Protocol):
    def process(self, data: WpDataFrame) -> WpDataFrame: ...
    def validate(self) -> bool: ...

# 使用TypedDict定义结构化数据
class OptimizationConfig(TypedDict):
    algorithm: Literal["pso", "genetic", "bayesian"]
    max_iterations: int
    tolerance: float
    parallel: NotRequired[bool]  # 可选字段
    seed: Required[int]          # 必需字段

# 使用泛型提高类型安全性
T = TypeVar('T', bound='DatasetProtocol')

class DatasetManager(Generic[T]):
    def add_dataset(self, name: str, dataset: T) -> None: ...
    def get_dataset(self, name: str) -> T | None: ...
```

### 6.2 异步编程支持

**ADG-R16** 大文件I/O和计算密集型操作支持异步模式：

```python
import asyncio
from typing import AsyncIterator

async def read_wp_excel_async(
    file_path: str,
    *,
    chunk_size: int = 10000
) -> AsyncIterator[WpDataFrame]:
    """异步读取WP文件，支持流式处理大文件。

    使用异步I/O避免阻塞主线程，适用于处理GB级别的大文件。

    Args:
        file_path: 文件路径
        chunk_size: 每次读取的行数

    Yields:
        WpDataFrame: 数据块

    Example:
        >>> async for chunk in read_wp_excel_async("large_file.xlsx"):
        ...     processed_chunk = await process_chunk_async(chunk)
        ...     await save_chunk_async(processed_chunk)
    """
    async with aiofiles.open(file_path, 'rb') as f:
        # 异步文件读取实现
        pass

async def process_chunk_async(chunk: WpDataFrame) -> WpDataFrame:
    """异步处理数据块。"""
    # 使用asyncio.to_thread将CPU密集型操作移到线程池
    return await asyncio.to_thread(_cpu_intensive_process, chunk)
```

### 6.3 GPU计算集成

**ADG-R17** 支持GPU加速的数值计算：

```python
def calculate_permeability_gpu(
    phit: np.ndarray,
    t2lm: np.ndarray,
    *,
    use_gpu: bool = True
) -> np.ndarray:
    """GPU加速的渗透率计算。

    当use_gpu=True且CUDA可用时，使用GPU加速计算。
    自动回退到CPU实现以确保兼容性。

    Args:
        phit: 孔隙度数组
        t2lm: T2对数平均值数组
        use_gpu: 是否尝试使用GPU加速

    Returns:
        np.ndarray: 渗透率数组

    Note:
        GPU加速需要安装cupy依赖：pip install cupy-cuda12x
    """
    if use_gpu and _cuda_available():
        import cupy as cp

        # 数据传输到GPU
        phit_gpu = cp.asarray(phit)
        t2lm_gpu = cp.asarray(t2lm)

        # GPU并行计算
        k_gpu = _foster_nmr_kernel(phit_gpu, t2lm_gpu)

        # 结果传回CPU
        return cp.asnumpy(k_gpu)
    else:
        # CPU向量化实现
        return _foster_nmr_cpu(phit, t2lm)

def _cuda_available() -> bool:
    """检查CUDA是否可用。"""
    try:
        import cupy
        return cupy.cuda.is_available()
    except ImportError:
        return False
```

## 7 质量检查清单

### 7.1 API文档检查清单

**ADG-R18** 每个API在发布前必须通过以下检查：

- [ ] **ADG-C1** 包含完整的Google风格docstring
- [ ] **ADG-C2** 所有参数都有类型注解和说明
- [ ] **ADG-C3** 返回值类型明确，复杂结构使用TypedDict
- [ ] **ADG-C4** 列出所有可能的异常及触发条件
- [ ] **ADG-C5** 包含实际可运行的使用示例
- [ ] **ADG-C6** 算法相关API包含数学公式和参数说明
- [ ] **ADG-C7** 引用相关的方法说明书章节
- [ ] **ADG-C8** 明确架构层次和依赖关系
- [ ] **ADG-C9** 使用常量枚举，避免硬编码字符串
- [ ] **ADG-C10** 支持现代化特性（异步、GPU等）

### 7.2 代码注释检查清单

**ADG-R19** 代码注释质量检查：

- [ ] **ADG-C11** 模块级文档字符串完整
- [ ] **ADG-C12** 复杂算法有详细行内注释
- [ ] **ADG-C13** 性能关键代码说明优化策略
- [ ] **ADG-C14** 业务逻辑注释解释决策依据
- [ ] **ADG-C15** 使用结构化日志记录关键事件
- [ ] **ADG-C16** 异常处理包含上下文信息
- [ ] **ADG-C17** 类型检查通过mypy验证
- [ ] **ADG-C18** 代码风格符合ruff规范

### 7.3 AI编程助手检查清单

**ADG-R20** AI编程助手生成代码前必须确认：

- [ ] **ADG-C29** 已严格遵循"杜绝臆测"原则
- [ ] **ADG-C30** 已通过工具确认所有API定义
- [ ] **ADG-C31** 已验证所有常量和枚举成员名称
- [ ] **ADG-C32** 已确认异常类的实际构造参数
- [ ] **ADG-C33** 已检查模块导入路径的准确性
- [ ] **ADG-C34** 已验证配置文件格式和字段名称
- [ ] **ADG-C35** 已执行完整的API探查流程
- [ ] **ADG-C36** 确认所有类型注解和方法签名
- [ ] **ADG-C37** 验证常量和枚举的正确使用
- [ ] **ADG-C38** 检查架构依赖关系的合规性
- [ ] **ADG-C39** 使用项目标准的异常类
- [ ] **ADG-C40** 遵循命名规范和代码风格
- [ ] **ADG-C41** 包含适当的日志记录
- [ ] **ADG-C42** 考虑性能和内存优化
- [ ] **ADG-C43** 支持现代化技术栈特性
- [ ] **ADG-C44** 文档引用正确的规范章节

---

## 附录

### A 参考文档

- [SCAPE_MS_方法说明书.md](./SCAPE_MS_方法说明书.md) - 算法科学依据
- [SCAPE_SAD_软件架构设计.md](./SCAPE_SAD_软件架构设计.md) - 架构设计原则
- [SCAPE_CCG_编码与通用规范.md](./SCAPE_CCG_编码与通用规范.md) - 编码规范
- [pyproject.toml](../pyproject.toml) - 项目配置和依赖
- [.cursorrules](../.cursorrules) - 开发规则

### B 工具链支持

- **ruff**: 代码质量检查和格式化
- **mypy**: 静态类型检查
- **sphinx**: 自动文档生成
- **pytest**: 单元测试和文档测试
- **structlog**: 结构化日志记录

### C 版本历史

- **v4.0** (2025-07-01): 基于最新架构和技术栈重写
- **v3.0** (2025-06-29): 重构后新架构版本 (已备份)
- **v2.x**: 历史版本 (已归档)
