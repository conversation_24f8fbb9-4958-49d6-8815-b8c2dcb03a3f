# Python浮点数比较编程规则

> **版本**: 1.0
> **更新日期**: 2025-07-12
> **适用范围**: SCAPE项目所有涉及浮点数计算的模块

---

## 1. 核心原则：永远不要直接比较浮点数

由于浮点数在计算机中的二进制表示存在固有的精度误差，直接使用 `==` 或 `!=` 进行比较是不可靠的，并且是 **严格禁止** 的。

**错误示例**:
```python
# 0.1 + 0.2 的结果并非精确的 0.3，而是 0.30000000000000004
result = 0.1 + 0.2

# ❌ 错误做法：这将返回 False，引发潜在的逻辑错误
if result == 0.3:
    print("相等")
```

## 2. 正确做法：基于容差的比较

所有浮点数的比较都必须通过判断两个数之差的绝对值是否小于一个预定义的、足够小的 **容差 (tolerance)** 来完成。

### 规则 2.1: 比较两个浮点数 `a` 和 `b`

**标准形式**: `abs(a - b) < tolerance`

```python
# ✅ 正确做法
tolerance = 1e-9
if abs(a - b) < tolerance:
    print("两个浮点数在容差范围内可视为相等")
```

### 规则 2.2: 比较浮点数 `a` 是否为零

**标准形式**: `abs(a) < tolerance`

在我们的代码库 `logwp/extras/nmr/internal/t2_porosity_compute.py` 中已有很好的实践：

```python
# 从常量模块导入阈值
from ..constants import DEFAULT_ZERO_THRESHOLD # 例如 1e-12

# ❌ 错误做法
# if single_t2_value != 0:

# ✅ 正确做法
if abs(single_t2_value) > DEFAULT_ZERO_THRESHOLD:
    # 执行非零逻辑
    ...
```

## 3. NumPy 数组的最佳实践：`np.isclose`

当处理 NumPy 数组时，应 **优先使用 `np.isclose()`** 函数。它内置了基于相对容差 (`rtol`) 和绝对容差 (`atol`) 的稳健比较逻辑。

**`np.isclose` 的判断公式**: `absolute(a - b) <= (atol + rtol * absolute(b))`

这在 `logwp/extras/nmr/internal/t2_porosity_compute.py` 中用于在 T2 轴上查找特定值：

```python
# ❌ 错误做法：在浮点数数组中直接查找，几乎总会失败
# indices = np.where(t2_time_full == single_t2_value)[0]

# ✅ 正确做法：使用 np.isclose
indices = np.where(np.isclose(t2_time_full, single_t2_value, rtol=1e-12, atol=1e-15))[0]
```

## 4. 规则总结与代码库实践

| 场景 | 错误做法 (`Don't`) | 正确做法 (`Do`) | 代码库参考 |
| :--- | :--- | :--- | :--- |
| **比较两个浮点数** | `a == b` | `abs(a - b) < TOLERANCE` | 通用数学计算 |
| **比较浮点数是否为零** | `a == 0` 或 `a != 0` | `abs(a) < ZERO_THRESHOLD` | `t2_porosity_compute.py` |
| **NumPy 数组比较** | `array == value` | `np.isclose(array, value)` | `t2_porosity_compute.py` |
| **替换数组中的“零”值** | `np.where(arr == 0, ...)` | `np.where(np.abs(arr) < TOL, ...)` | `pca/internal/compute.py` |

## 5. 容差管理：常量化

**严禁在代码中硬编码“魔术数字”作为容差**。所有容差值都应在 `constants.py` 文件中统一定义，并赋予有意义的名称，以便于全局管理和调整。

**代码库参考**: `logwp/extras/nmr/constants.py`
```python
# 数值计算默认参数
DEFAULT_NUMERICAL_TOLERANCE: Final[float] = 1e-10
DEFAULT_ZERO_THRESHOLD: Final[float] = 1e-12

# 数值稳定性常量
MACHINE_EPSILON: Final[float] = 2.220446049250313e-16
```

## 6. 代码审查清单 (Code Review Checklist)

在代码审查时，请检查以下几点：

- [ ] **是否存在 `==` 或 `!=` 用于浮点数比较？**
  - 如果存在，必须修改为基于容差的比较。

- [ ] **是否使用了 `np.isclose` 来比较 NumPy 浮点数数组？**
  - 对于 NumPy 数组，`np.isclose` 是首选方案。

- [ ] **是否所有容差值都来自于 `constants.py` 模块？**
  - 避免在代码中硬编码容差值（如 `1e-9`, `1e-12`）。

- [ ] **容差值的选择是否合理，并有注释说明？**
  - 容差的选择应根据具体业务场景和数据精度确定。

---

**参考文档：**
- SCAPE_CCG_编码与通用规范.md
- What Is the Best Way to Compare Floats for Almost-Equality?

