# 任何项目都务必遵守的规则（极其重要！！！）

## Communication

- 请使用英文进行思考，但使用简体中文对话

## Code Architecture

- 编写代码的硬性指标，包括以下原则：

  （1）对于 Python、JavaScript、TypeScript 等动态语言，尽可能确保每个代码文件不要超过 200 行

  （2）对于 Java、Go、Rust 等静态语言，尽可能确保每个代码文件不要超过 250 行

  （3）每层文件夹中的文件，尽可能不超过 8 个。如有超过，需要规划为多层子文件夹

- 除了硬性指标以外，还需要时刻关注优雅的架构设计，避免出现以下可能侵蚀我们代码质量的「坏味道」：

  （1）僵化 (Rigidity): 系统难以变更，任何微小的改动都会引发一连串的连锁修改。

  （2）冗余 (Redundancy): 同样的代码逻辑在多处重复出现，导致维护困难且容易产生不一致。

  （3）循环依赖 (Circular Dependency): 两个或多个模块互相纠缠，形成无法解耦的“死结”，导致难以测试与复用。

  （4）脆弱性 (Fragility): 对代码一处的修改，导致了系统中其他看似无关部分功能的意外损坏。

  （5）晦涩性 (Obscurity): 代码意图不明，结构混乱，导致阅读者难以理解其功能和设计。

  （6）数据泥团 (Data Clump): 多个数据项总是一起出现在不同方法的参数中，暗示着它们应该被组合成一个独立的对象。

  （7）不必要的复杂性 (Needless Complexity): 用“杀牛刀”去解决“杀鸡”的问题，过度设计使系统变得臃肿且难以理解。

- 【非常重要！！】无论是你自己编写代码，还是阅读或审核他人代码时，都要严格遵守上述硬性指标，以及时刻关注优雅的架构设计。

- 【非常重要！！】无论何时，一旦你识别出那些可能侵蚀我们代码质量的「坏味道」，都应当立即询问用户是否需要优化，并给出合理的优化建议。

---

## 项目核心文档命名及目录存放规范

- 项目核心文档全部放在`/docs`目录下

### SCAPE方法文档
- **方法说明书/MS**：`SCAPE_MS_方法说明书.md` - SCAPE项目目标和科学定义，所有实现的基准

### wp测井数据格式规范文档
- **WP文件规范/WFS**：`SCAPE_WFS_WP文件规范.md` - WP数据文件格式规范、LAS转换规则

### 架构设计文档
- **软件架构设计/SAD**：在`/docs/sdd`目录下，文件名以`SCAPE_SAD_*.md`命名，其中`SCAPE_SAD_logwp.md` - 为logwp包的详细设计提供背景，包括系统整体架构、设计原则、技术选型
- **logwp详细设计/DDS**：在`/docs/sdd`目录下， 包含各个具体子模块的详细设计，文件名以`SCAPE_DDS_logwp_*.md`命名
- **设计文档**：上述SAD和DDS文档的统称

### 编码规范文档
- **编码与通用规范/CCG**：在`/docs/sdd`目录下，文件名以`SCAPE_CCG_*.md`命名，`SCAPE_CCG_编码与通用规范.md` - 代码风格、命名约定、异常处理等
- **API文档注释规范/ADG**：在`/docs/sdd`目录下，文件名以`SCAPE_ADG_*.md`命名，`SCAPE_ADG_API文档注释规范.md` - API文档和代码注释规范

### 测试规范文档
- **软件测试指南/STG**：在`/docs/sdd`目录下，文件名以`SCAPE_STG_*.md`命名，`SCAPE_STG_软件测试指南.md` - 测试策略、用例设计、执行规范

### logwp包API使用文档
- **API使用文档/API**：在`/docs/sdd`目录下，文件名以`SCAPE_API_*.md`命名，`SCAPE_API_logwp_models.md` - logwp.models包API使用文档

### examples文档
- 位于`/docs/examples`目录下，用于存放示例代码和数据

### 开发计划和日志文档
- 位于`/docs/sdp`目录下，包括`DEV_PLAN.md`和`DEV_LOG.md`等


### 文档内容优级
- 随着开发的进行，有些文档内容会部分过时，此时的参考优先级是：代码库 > 最新文档 > 历史文档
- 最新文档的顺序是：API文档>SAD文档>DDS文档>其它文档

**使用说明**：
- 在代码、注释、文档中应使用统一简称进行引用
- 在编写或生成文档时，请按照类别存放入相应的目录下
- 上述文档的文件名中如果出现`老版本`字样，表示该文档为历史版本，其中的内容与代码库当前状态可能有不一致的现象，请以代码库为准
