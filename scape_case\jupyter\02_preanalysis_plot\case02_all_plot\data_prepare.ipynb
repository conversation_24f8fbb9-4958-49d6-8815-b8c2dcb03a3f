{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 基础绘图数据准备"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-04T02:12:35.844343Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 458.98, 'cpu_percent': 0.0} file_path=santos_data_v2.wp.xlsx\n", "2025-08-04T02:12:35.931228Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} file_path=santos_data_v2.wp.xlsx file_size_mb=21.82 sheet_count=8\n", "2025-08-04T02:12:35.957343Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} project_name=santos_data_v2\n", "2025-08-04T02:12:35.975248Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v2\n", "2025-08-04T02:12:35.997508Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-08-04T02:12:36.027523Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-08-04T02:12:36.062326Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-08-04T02:12:36.093154Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-08-04T02:12:36.122263Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.12, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:12:36.192432Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.16, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=79 well_curves=1\n", "2025-08-04T02:13:02.576425Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.48, 'cpu_percent': 0.0} shape=(16303, 268) sheet_name=Logs\n", "2025-08-04T02:13:02.661379Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.76, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-08-04T02:13:02.682749Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.76, 'cpu_percent': 0.0} curve_count=79 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 268) processing_time=26.566\n", "2025-08-04T02:13:02.721724Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.81, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:02.787796Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.81, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-08-04T02:13:07.556824Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.95, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-08-04T02:13:07.609883Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.95, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-08-04T02:13:07.634690Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.95, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=4.919\n", "2025-08-04T02:13:07.679957Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.98, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:07.717865Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.98, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-08-04T02:13:07.837562Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-08-04T02:13:07.884234Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-08-04T02:13:07.920150Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.245\n", "2025-08-04T02:13:07.966912Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-08-04T02:13:07.993058Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-08-04T02:13:08.038897Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-08-04T02:13:08.063392Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-08-04T02:13:08.080786Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.118\n", "2025-08-04T02:13:08.115843Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:08.143534Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-08-04T02:13:08.175300Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-08-04T02:13:08.194797Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-08-04T02:13:08.212303Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.101\n", "2025-08-04T02:13:08.249542Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-08-04T02:13:08.274923Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataset_count=5\n", "2025-08-04T02:13:08.292645Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.07, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v2.wp.xlsx processing_time=32.448 project_name=WpIdentifier('santos_data_v2')\n", "2025-08-04T02:13:08.337429Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.15, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v2'}\n", "2025-08-04T02:13:08.416886Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.18, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v2\n", "📅 创建时间: 2025-08-04 10:12:35.975248\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"./santos_data_v2.wp.xlsx\" #原始数据，使用精校过的数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取数据"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["\n", "# 不含二维\n", "logs_curves = [\n", "    'GR', 'BS', 'CAL', 'DEN', 'CN', 'DT', 'PE',\n", "    'RD', 'RS', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'RD_LWD', 'RS_LWD', 'RD_LWD_LOG10', 'RS_LWD_LOG10', 'DRES_LWD',\n", "    'PHIT_NMR', 'PHIE_NMR', 'CBW_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD',\n", "    'T2CUTOFF', 'T2LM', 'T2LM_LOG10', 'T2LM_LONG', 'T2LM_LONG_LOG10',\n", "    'T2_P20', 'T2_P20_LOG10', 'T2_P50', 'T2_P50_LOG10',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'\n", "]\n", "\n", "k_label_curves = [\n", "     'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "]\n", "\n", "crossplot_curves = [\n", "    'RD', 'RS',  'DRES',\n", "    'RD_LWD', 'RS_LWD',  'DRES_LWD',\n", "    'T2CUTOFF', 'T2LM', 'T2LM_LONG',\n", "    'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'\n", "]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-04T02:13:08.509215Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.35, 'cpu_percent': 0.0} curve_count=3 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_ternary\n", "2025-08-04T02:13:08.549602Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.35, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['VMACRO', 'WELL_NO', 'VMESO', 'VMICRO', 'MD'] operation=extract_metadata output_curve_count=5 output_curves=['VMACRO', 'WELL_NO', 'VMESO', 'VMICRO', 'MD']\n", "2025-08-04T02:13:08.830222Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.73, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:08.861576Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.75, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-04T02:13:08.904819Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.75, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_ternary target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-04T02:13:08.959734Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.75, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:08.989622Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.75, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO', 'VMESO', 'VMACRO'] operation=extract_curves selected_columns=5 source_rows=16303 target_rows=14285\n", "2025-08-04T02:13:09.056061Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=3 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 458.36, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-04T02:13:09.077601Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 458.36, 'cpu_percent': 0.0} curve_count=3 dropna_how=any new_dataset=nmr_ternary_cleaned operation=dropna_dataset source_dataset=nmr_ternary\n", "2025-08-04T02:13:09.113646Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=14283 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.97, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14285 removed_rows=2\n", "2025-08-04T02:13:09.160939Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.43, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-04T02:13:09.206389Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.43, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:09.233978Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.43, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-04T02:13:09.268278Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.43, 'cpu_percent': 0.0} curve_count=3 has_query=True operation=extract_curves source_dataset=logs target_dataset=lwd_nmr_ternary\n", "2025-08-04T02:13:09.295995Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.43, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['VMESO_LWD', 'WELL_NO', 'VMICRO_LWD', 'MD', 'VMACRO_LWD'] operation=extract_metadata output_curve_count=5 output_curves=['VMESO_LWD', 'WELL_NO', 'VMICRO_LWD', 'MD', 'VMACRO_LWD']\n", "2025-08-04T02:13:09.604766Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 490.36, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_lwd_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:09.635805Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 490.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-04T02:13:09.682908Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 490.38, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=lwd_nmr_ternary target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-04T02:13:09.740124Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 490.38, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:09.783222Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 490.38, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD'] operation=extract_curves selected_columns=5 source_rows=16303 target_rows=14285\n", "2025-08-04T02:13:09.842754Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=3 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-04T02:13:09.862996Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} curve_count=3 dropna_how=any new_dataset=lwd_nmr_ternary_cleaned operation=dropna_dataset source_dataset=lwd_nmr_ternary\n", "2025-08-04T02:13:09.890977Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2443 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.07, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14285 removed_rows=11842\n", "2025-08-04T02:13:09.919928Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.16, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-08-04T02:13:09.948933Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.16, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:09.983703Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.16, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-04T02:13:10.003431Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.16, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['VMICRO', 'VMESO', 'VMACRO'] left_dataset=nmr_ternary_cleaned max_interpolation_distance=0.075 operation=merge_datasets_left_aligned right_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_dataset=K_Label\n", "2025-08-04T02:13:10.052129Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.16, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=nmr_ternary_cleaned target_dataset=nmr_ternary_cleaned_left_temp\n", "2025-08-04T02:13:10.105063Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.16, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['VMACRO', 'WELL_NO', 'VMESO', 'VMICRO', 'MD'] operation=extract_metadata output_curve_count=5 output_curves=['VMACRO', 'WELL_NO', 'VMESO', 'VMICRO', 'MD']\n", "2025-08-04T02:13:10.148813Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.79, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:10.185048Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.34, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-04T02:13:10.225517Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.34, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_ternary_cleaned_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-04T02:13:10.280362Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:10.312285Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.34, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO', 'VMESO', 'VMACRO'] operation=extract_curves selected_columns=5 source_rows=14283 target_rows=14283\n", "2025-08-04T02:13:10.342716Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.34, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_right_temp\n", "2025-08-04T02:13:10.386035Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.34, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI']\n", "2025-08-04T02:13:10.435298Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:10.475898Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-08-04T02:13:10.517882Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_right_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-08-04T02:13:10.570263Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:10.606081Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=506 target_rows=506\n", "2025-08-04T02:13:10.659554Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.03809999999975844 operation=merge_datasets_left_aligned\n", "2025-08-04T02:13:10.687221Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI']\n", "2025-08-04T02:13:10.716472Z [info     ] 开始转换右数据集为兼容格式（左对齐合并）           [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.38, 'cpu_percent': 0.0} operation=_convert_right_dataset_to_compatible_format right_dataset_type=WpDiscreteDataset\n", "2025-08-04T02:13:10.811821Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.98, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:10.840721Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.98, 'cpu_percent': 0.0} left_rows=14283 operation=_merge_dataframes_left_aligned right_data_columns=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_rows=14283\n", "2025-08-04T02:13:10.885574Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.71, 'cpu_percent': 0.0} merged_columns=8 merged_rows=14283 operation=_merge_dataframes_left_aligned\n", "2025-08-04T02:13:10.928422Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.71, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary_k dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:10.983559Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.71, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'VMICRO', 'VMESO', 'VMACRO', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('nmr_ternary_k') result_dataset_type=WpContinuousDataset result_shape=(14283, 8)\n", "2025-08-04T02:13:11.013025Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.63, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD'] left_dataset=lwd_nmr_ternary_cleaned max_interpolation_distance=0.075 operation=merge_datasets_left_aligned right_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_dataset=K_Label\n", "2025-08-04T02:13:11.046035Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.63, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=lwd_nmr_ternary_cleaned target_dataset=lwd_nmr_ternary_cleaned_left_temp\n", "2025-08-04T02:13:11.071979Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.63, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['VMESO_LWD', 'WELL_NO', 'VMICRO_LWD', 'MD', 'VMACRO_LWD'] operation=extract_metadata output_curve_count=5 output_curves=['VMESO_LWD', 'WELL_NO', 'VMICRO_LWD', 'MD', 'VMACRO_LWD']\n", "2025-08-04T02:13:11.102333Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_lwd_nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:11.141237Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-08-04T02:13:11.170966Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=lwd_nmr_ternary_cleaned_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-08-04T02:13:11.199903Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:11.238017Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD'] operation=extract_curves selected_columns=5 source_rows=2443 target_rows=2443\n", "2025-08-04T02:13:11.286277Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_right_temp\n", "2025-08-04T02:13:11.335152Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI']\n", "2025-08-04T02:13:11.381245Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:11.430452Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-08-04T02:13:11.467775Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_right_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-08-04T02:13:11.528534Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:11.566033Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=506 target_rows=506\n", "2025-08-04T02:13:11.598894Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.15239999999994325 operation=merge_datasets_left_aligned\n", "2025-08-04T02:13:11.640730Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI']\n", "2025-08-04T02:13:11.668672Z [info     ] 开始转换右数据集为兼容格式（左对齐合并）           [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.68, 'cpu_percent': 0.0} operation=_convert_right_dataset_to_compatible_format right_dataset_type=WpDiscreteDataset\n", "2025-08-04T02:13:11.736085Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.88, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:11.755944Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.88, 'cpu_percent': 0.0} left_rows=2443 operation=_merge_dataframes_left_aligned right_data_columns=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_rows=2443\n", "2025-08-04T02:13:11.800314Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.89, 'cpu_percent': 0.0} merged_columns=8 merged_rows=2443 operation=_merge_dataframes_left_aligned\n", "2025-08-04T02:13:11.837163Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.89, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary_k dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:11.865825Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.89, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('lwd_nmr_ternary_k') result_dataset_type=WpContinuousDataset result_shape=(2443, 8)\n", "2025-08-04T02:13:11.892993Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.89, 'cpu_percent': 0.0} curve_count=15 has_query=True operation=extract_curves source_dataset=logs target_dataset=crossplot\n", "2025-08-04T02:13:11.916203Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.89, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['T2LM', 'RD', 'DT2LM', 'RS', 'DT2_P20', 'DPHIT_NMR', 'WELL_NO', 'DRES_LWD', 'MD', 'RS_LWD', 'RD_LWD', 'DRES', 'DT2LM_LONG', 'T2CUTOFF', 'DPHIE_NMR', 'DT2_P50', 'T2LM_LONG'] operation=extract_metadata output_curve_count=17 output_curves=['T2LM', 'RD', 'DT2LM', 'RS', 'DT2_P20', 'DPHIT_NMR', 'WELL_NO', 'DRES_LWD', 'MD', 'RS_LWD', 'RD_LWD', 'DRES', 'DT2LM_LONG', 'T2CUTOFF', 'DPHIE_NMR', 'DT2_P50', 'T2LM_LONG']\n", "2025-08-04T02:13:12.197874Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_crossplot dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:12.229980Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.37, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-04T02:13:12.270584Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.37, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=crossplot target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-04T02:13:12.299330Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.37, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=crossplot dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:12.323924Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.37, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'RD', 'RS', 'DRES', 'RD_LWD', 'RS_LWD', 'DRES_LWD', 'T2CUTOFF', 'T2LM', 'T2LM_LONG', 'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'] operation=extract_curves selected_columns=17 source_rows=16303 target_rows=14285\n", "2025-08-04T02:13:12.369988Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=15 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.5, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-04T02:13:12.388482Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.5, 'cpu_percent': 0.0} curve_count=15 dropna_how=any new_dataset=crossplot_cleaned operation=dropna_dataset source_dataset=crossplot\n", "2025-08-04T02:13:12.424416Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1709 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.93, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14285 removed_rows=12576\n", "2025-08-04T02:13:12.467071Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.95, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-08-04T02:13:12.513734Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.95, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=crossplot_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:12.546030Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.95, 'cpu_percent': 0.0} operation=dropna_dataset\n", "  WELL_NO         MD       RD      RS      DRES   RD_LWD   RS_LWD  DRES_LWD  \\\n", "0     C-1  6335.2680  397.125  1950.0  0.691107  203.991  225.784  0.044082   \n", "1     C-1  6335.5728  507.629  1950.0  0.584488  356.866  232.630 -0.185839   \n", "2     C-1  6335.7252  543.479  1950.0  0.554852  281.235  232.868 -0.081961   \n", "3     C-1  6336.4872  527.144  1950.0  0.568105  232.630  254.614  0.039216   \n", "4     C-1  6336.6396  499.574  1950.0  0.591435  302.308  319.096  0.023472   \n", "\n", "   T2CUTOFF     T2LM  T2LM_LONG     DT2LM  DT2LM_LONG   DT2_P50   DT2_P20  \\\n", "0       100  746.054    899.674  0.320085    0.160495  0.111100  0.058589   \n", "1       100  690.019    965.594  0.363240    0.340045  0.144534  0.317808   \n", "2       100  780.266   1036.484  0.392162    0.570361  0.300230  0.437292   \n", "3       100  141.238   1138.436 -0.604647    0.639850 -0.051744  0.320758   \n", "4       100  253.915   1100.064  0.137229    0.526200  0.188753  0.177327   \n", "\n", "   DPHIT_NMR  DPHIE_NMR  \n", "0   0.023800   0.029760  \n", "1  -0.004797   0.001548  \n", "2  -0.004615  -0.003475  \n", "3  -0.001553  -0.016294  \n", "4  -0.002808  -0.003170  \n", "2025-08-04T02:13:12.571750Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.95, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['RD', 'RS', 'DRES', 'RD_LWD', 'RS_LWD', 'DRES_LWD', 'T2CUTOFF', 'T2LM', 'T2LM_LONG', 'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'] left_dataset=crossplot_cleaned max_interpolation_distance=0.075 operation=merge_datasets_left_aligned right_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_dataset=K_Label\n", "2025-08-04T02:13:12.609805Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.95, 'cpu_percent': 0.0} curve_count=15 has_query=False operation=extract_curves source_dataset=crossplot_cleaned target_dataset=crossplot_cleaned_left_temp\n", "2025-08-04T02:13:12.635500Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 462.95, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['T2LM', 'RD', 'DT2LM', 'RS', 'DT2_P20', 'DPHIT_NMR', 'WELL_NO', 'DRES_LWD', 'MD', 'RS_LWD', 'RD_LWD', 'DRES', 'DT2LM_LONG', 'T2CUTOFF', 'DPHIE_NMR', 'DT2_P50', 'T2LM_LONG'] operation=extract_metadata output_curve_count=17 output_curves=['T2LM', 'RD', 'DT2LM', 'RS', 'DT2_P20', 'DPHIT_NMR', 'WELL_NO', 'DRES_LWD', 'MD', 'RS_LWD', 'RD_LWD', 'DRES', 'DT2LM_LONG', 'T2CUTOFF', 'DPHIE_NMR', 'DT2_P50', 'T2LM_LONG']\n", "2025-08-04T02:13:12.679187Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_crossplot_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:12.720713Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-08-04T02:13:12.770661Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=crossplot_cleaned_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-08-04T02:13:12.811839Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=crossplot_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:12.854736Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'RD', 'RS', 'DRES', 'RD_LWD', 'RS_LWD', 'DRES_LWD', 'T2CUTOFF', 'T2LM', 'T2LM_LONG', 'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'] operation=extract_curves selected_columns=17 source_rows=1709 target_rows=1709\n", "2025-08-04T02:13:12.899127Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_right_temp\n", "2025-08-04T02:13:12.934035Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.34, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI']\n", "2025-08-04T02:13:12.969943Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:13.009569Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-08-04T02:13:13.058213Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_right_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-08-04T02:13:13.114041Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-04T02:13:13.152658Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=506 target_rows=506\n", "2025-08-04T02:13:13.193525Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.15239999999994325 operation=merge_datasets_left_aligned\n", "2025-08-04T02:13:13.218802Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['WELL_NO', 'K_LABEL_TYPE', 'K_LABEL', 'MD', 'PZI']\n", "2025-08-04T02:13:13.236346Z [info     ] 开始转换右数据集为兼容格式（左对齐合并）           [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.37, 'cpu_percent': 0.0} operation=_convert_right_dataset_to_compatible_format right_dataset_type=WpDiscreteDataset\n", "2025-08-04T02:13:13.274744Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.18, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:13.296236Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.18, 'cpu_percent': 0.0} left_rows=1709 operation=_merge_dataframes_left_aligned right_data_columns=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_rows=1709\n", "2025-08-04T02:13:13.325015Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.64, 'cpu_percent': 0.0} merged_columns=20 merged_rows=1709 operation=_merge_dataframes_left_aligned\n", "2025-08-04T02:13:13.354512Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=crossplot_k dataset_type=Continuous operation=dataset_initialization\n", "2025-08-04T02:13:13.381399Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.71, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'RD', 'RS', 'DRES', 'RD_LWD', 'RS_LWD', 'DRES_LWD', 'T2CUTOFF', 'T2LM', 'T2LM_LONG', 'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('crossplot_k') result_dataset_type=WpContinuousDataset result_shape=(1709, 20)\n", "2025-08-04T02:13:13.407883Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.71, 'cpu_percent': 0.0} dataset_count=1 file_path=nmr_ternary.wp.xlsx project_name=WpIdentifier('nmr_ternary') save_head_info=True save_well_map=True\n", "2025-08-04T02:13:13.450706Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.71, 'cpu_percent': 0.0} curve_count=8 dataset_name=WpIdentifier('nmr_ternary_k') dataset_type=Continuous df_shape=(14283, 8)\n", "2025-08-04T02:13:14.151070Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.49, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_ternary_k') processing_time=0.7\n", "2025-08-04T02:13:14.171059Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.49, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-08-04T02:13:14.199514Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.49, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:14.209666Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.49, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-08-04T02:13:17.801774Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.66, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:17.821607Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.66, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:20.078310Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 476.1, 'cpu_percent': 0.0} file_path=nmr_ternary.wp.xlsx processing_time=6.67 project_name=WpIdentifier('nmr_ternary')\n", "✅WL数据已保存: nmr_ternary.wp.xlsx\n", "2025-08-04T02:13:20.103682Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 476.1, 'cpu_percent': 0.0} dataset_count=1 file_path=lwd_nmr_ternary.wp.xlsx project_name=WpIdentifier('lwd_nmr_ternary') save_head_info=True save_well_map=True\n", "2025-08-04T02:13:20.143112Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 476.1, 'cpu_percent': 0.0} curve_count=8 dataset_name=WpIdentifier('lwd_nmr_ternary_k') dataset_type=Continuous df_shape=(2443, 8)\n", "2025-08-04T02:13:20.261856Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.41, 'cpu_percent': 0.0} dataset_name=WpIdentifier('lwd_nmr_ternary_k') processing_time=0.119\n", "2025-08-04T02:13:20.288767Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.41, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-08-04T02:13:20.334277Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.41, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:20.360852Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.41, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-08-04T02:13:21.015279Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.41, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:21.034803Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.41, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:21.491993Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.46, 'cpu_percent': 0.0} file_path=lwd_nmr_ternary.wp.xlsx processing_time=1.388 project_name=WpIdentifier('lwd_nmr_ternary')\n", "✅LWD数据已保存: lwd_nmr_ternary.wp.xlsx\n", "2025-08-04T02:13:21.519523Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.46, 'cpu_percent': 0.0} dataset_count=1 file_path=crossplot.wp.xlsx project_name=WpIdentifier('crossplot') save_head_info=True save_well_map=True\n", "2025-08-04T02:13:21.576399Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 477.46, 'cpu_percent': 0.0} curve_count=20 dataset_name=WpIdentifier('crossplot_k') dataset_type=Continuous df_shape=(1709, 20)\n", "2025-08-04T02:13:21.759777Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.79, 'cpu_percent': 0.0} dataset_name=WpIdentifier('crossplot_k') processing_time=0.183\n", "2025-08-04T02:13:21.773318Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.79, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-08-04T02:13:21.790579Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.79, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:21.809501Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.79, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-08-04T02:13:22.826918Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.42, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:22.848242Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.42, 'cpu_percent': 0.0}\n", "2025-08-04T02:13:23.517028Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 484.06, 'cpu_percent': 0.0} file_path=crossplot.wp.xlsx processing_time=1.998 project_name=WpIdentifier('crossplot')\n", "✅CrossPlot数据已保存: crossplot.wp.xlsx\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "\n", "    try:\n", "\n", "        # WL数据\n", "        nmr_ternary_ds = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_ternary\",\n", "            curve_names=['VMICRO','VMESO','VMACRO'],\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"nmr_ternary\", nmr_ternary_ds)\n", "        nmr_ternary_ds = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_ternary\",\n", "            curve_names=[],\n", "            new_dataset_name=\"nmr_ternary_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "        project.add_dataset(\"nmr_ternary_cleaned\", nmr_ternary_ds)\n", "\n", "        #LWD数据\n", "        lwd_nmr_ternary_ds = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"lwd_nmr_ternary\",\n", "            curve_names=['VMICRO_LWD','VMESO_LWD','VMACRO_LWD'],\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"lwd_nmr_ternary\", lwd_nmr_ternary_ds)\n", "        lwd_nmr_ternary_ds = project.dropna_dataset(\n", "            source_dataset_name=\"lwd_nmr_ternary\",\n", "            curve_names=[],\n", "            new_dataset_name=\"lwd_nmr_ternary_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "        project.add_dataset(\"lwd_nmr_ternary_cleaned\", lwd_nmr_ternary_ds)\n", "\n", "        # WL数据合并\n", "        merge_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"nmr_ternary_cleaned\",\n", "            left_curves=['VMICRO','VMESO','VMACRO'],\n", "            right_dataset=\"K_Label\",\n", "            right_curves=k_label_curves,\n", "            interpolation_method = \"nearest\",\n", "            max_interpolation_distance=0.075, # 采样间隔的一半\n", "            new_dataset_name=\"nmr_ternary_k\"\n", "        )\n", "\n", "        # LWD数据合并\n", "        lwd_merge_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"lwd_nmr_ternary_cleaned\",\n", "            left_curves=['VMICRO_LWD','VMESO_LWD','VMACRO_LWD'],\n", "            right_dataset=\"K_Label\",\n", "            right_curves=k_label_curves,\n", "            interpolation_method = \"nearest\",\n", "            max_interpolation_distance=0.075, # 采样间隔的一半\n", "            new_dataset_name=\"lwd_nmr_ternary_k\"\n", "        )\n", "\n", "        # Crossplot数据\n", "        crossplot_ds = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"crossplot\",\n", "            curve_names=crossplot_curves,\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"crossplot\", crossplot_ds)\n", "        crossplot_ds = project.dropna_dataset(\n", "            source_dataset_name=\"crossplot\",\n", "            curve_names=[],\n", "            new_dataset_name=\"crossplot_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "        project.add_dataset(\"crossplot_cleaned\", crossplot_ds)\n", "\n", "        print (crossplot_ds.df.head())\n", "        crossplot_merge_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"crossplot_cleaned\",\n", "            left_curves=crossplot_curves,\n", "            right_dataset=\"K_Label\",\n", "            right_curves=k_label_curves,\n", "            interpolation_method = \"nearest\",\n", "            max_interpolation_distance=0.075, # 采样间隔的一半\n", "            new_dataset_name=\"crossplot_k\"\n", "        )\n", "\n", "\n", "        # 保存WL\n", "        temp_project = WpWellProject(name=\"nmr_ternary\")\n", "        temp_project.add_dataset(\"nmr_ternary\", merge_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        out_path = \"nmr_ternary.wp.xlsx\"\n", "        writer.write(temp_project, out_path, apply_formatting=True)\n", "\n", "        print(f\"✅WL数据已保存: {out_path}\")\n", "\n", "        # 保存LWD\n", "        temp_project = WpWellProject(name=\"lwd_nmr_ternary\")\n", "        temp_project.add_dataset(\"lwd_nmr_ternary\", lwd_merge_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        out_path = \"lwd_nmr_ternary.wp.xlsx\"\n", "        writer.write(temp_project, out_path, apply_formatting=True)\n", "        print(f\"✅LWD数据已保存: {out_path}\")\n", "\n", "        # 保存CrossPlot\n", "        temp_project = WpWellProject(name=\"crossplot\")\n", "        temp_project.add_dataset(\"crossplot\", crossplot_merge_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        out_path = \"crossplot.wp.xlsx\"\n", "        writer.write(temp_project, out_path, apply_formatting=True)\n", "        print(f\"✅CrossPlot数据已保存: {out_path}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}