"""logwp.extras.petroplot.crossplot.plot_profiles - 交会图绘图模板

本模块负责定义并向全局注册表注册交会图组件所需的所有`PlotProfile`模板。

它遵循两级继承体系：
- `petroplot.crossplot.base`: 定义所有交会图共有的基础样式（如网格、标记大小）。
- `petroplot.crossplot.default`: 用户直接使用的默认模板，继承自base。

通过在 `crossplot/__init__.py` 中导入此模块，可以实现模板的自动注册。

Architecture
------------
层次/依赖: petroplot/crossplot配置层，被plotting.registry使用
设计原则: 导入即注册、样式与逻辑分离、两级继承
"""

from logwp.extras.plotting import PlotProfile, SaveConfig, registry
from .constants import CrossPlotProfiles


def _create_base_profile() -> PlotProfile:
    """创建所有交会图的基础PlotProfile。"""
    return PlotProfile(
        name=CrossPlotProfiles.BASE.value,
        # rc_params将从全局'base'模板继承，这里只定义交会图特有的覆盖项
        rc_params={
            "axes.grid": True,  # 交会图默认开启网格
            "grid.linestyle": "--",
            "grid.linewidth": 0.5,
        },
        figure_props={
            "figsize": (8, 7),  # 为交会图设置一个合适的默认宽高比
            "dpi": 150,
        },
        artist_props={
            "scatter": {
                "size": 8,
                "line": {"width": 0.5, "color": "white"}
            },
            "line": {
                "width": 2
                # 'color' and 'dash' will typically be assigned per-series
            },
            "diagonal_line": {
                "color": "grey",
                "width": 1.5,
                "dash": "dash"
            },
            "marginal_histogram": {
                "marker_color": "grey",
                "opacity": 0.7
            },
            "marginal_rug": {
                "color": "black"
            },
            "marginal_box": {
                "marker_color": "grey",
                "line_color": "black",
                "boxmean": True
            },
            "axis": {
                "linewidth": 1.5,
                "ticks": "outside",
                "titlefont": {"size": 12},
                "tickfont": {"size": 10},
            },
            "colorbar": {
                "title_side": "right",
                "titlefont": {"size": 10},
                "tickfont": {"size": 8},
            }
        },
        save_config=SaveConfig(format=["html", "png"], dpi=300)
    )


def _create_default_profile() -> PlotProfile:
    """创建用户直接使用的默认PlotProfile。"""
    return PlotProfile(name=CrossPlotProfiles.DEFAULT.value)


# 在模块加载时，立即执行注册
registry.register_base(_create_base_profile())
registry.register(_create_default_profile())
